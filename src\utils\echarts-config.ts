// ECharts 全局配置 - 解决被动事件监听器问题
import * as echarts from 'echarts';

// 按需加载 ECharts GL 扩展（暂时禁用以避免重复注册）
const loadEChartsGL = () => {
  // 动态导入 echarts-gl 扩展
  import('echarts-gl').then(() => {
    console.log('ECharts GL 扩展已加载');
  }).catch(error => {
    console.warn('ECharts GL 扩展加载失败:', error);
  });
};

// 配置 ECharts 全局选项
export const configureECharts = () => {
  // 暂时禁用 ECharts GL 扩展的自动加载，避免重复注册问题
  // 如需要 3D 图表功能，可取消下面的注释
  // loadEChartsGL();

  // 设置全局配置，启用被动事件监听器
  if (typeof window !== 'undefined') {
    // 重写 addEventListener 方法，为 wheel 和 mousewheel 事件添加 passive 选项
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    EventTarget.prototype.addEventListener = function(type, listener, options) {
      if (type === 'wheel' || type === 'mousewheel') {
        if (typeof options === 'boolean') {
          options = { capture: options, passive: true };
        } else if (typeof options === 'object' && options !== null) {
          options = { ...options, passive: true };
        } else {
          options = { passive: true };
        }
      }
      return originalAddEventListener.call(this, type, listener, options);
    };
  }
};

// ECharts 初始化配置
export const getEChartsInitOptions = () => ({
  renderer: 'canvas',
  useDirtyRect: false,
  useCoarsePointer: false,
  pointerSize: 'auto',
  ssr: false,
  width: 'auto',
  height: 'auto',
  locale: 'ZH'
});

// ECharts setOption 配置
export const getEChartsSetOptionConfig = () => ({
  notMerge: false,
  lazyUpdate: false,
  silent: false
});

// 修复 ECharts 颜色和工具提示问题的专用函数
export const fixEChartsColorIssues = (option: any) => {
  if (!option || typeof option !== 'object') {
    return option;
  }

  // 确保全局颜色调色板存在
  const defaultColors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'];
  if (!option.color || !Array.isArray(option.color)) {
    option.color = defaultColors;
  }

  // 修复 series 中的颜色配置
  if (option.series && Array.isArray(option.series)) {
    option.series = option.series.map((series: any, index: number) => {
      if (!series || typeof series !== 'object') {
        return series;
      }

      const seriesColor = series.itemStyle?.color ||
                         series.lineStyle?.color ||
                         option.color[index % option.color.length];

      // 确保每个 series 都有完整的颜色配置
      return {
        ...series,
        itemStyle: {
          color: seriesColor,
          ...series.itemStyle
        },
        lineStyle: series.type === 'line' ? {
          color: seriesColor,
          ...series.lineStyle
        } : series.lineStyle,
        areaStyle: (series.type === 'line' && series.areaStyle) ? {
          color: seriesColor,
          opacity: 0.3,
          ...series.areaStyle
        } : series.areaStyle
      };
    });
  }

  return option;
};

// 安全的 ECharts 初始化方法
export const safeInitECharts = (container: HTMLElement, theme?: string | object) => {
  try {
    if (!container) {
      throw new Error('容器元素不存在');
    }

    // 使用最基本的初始化选项
    const initOptions = {
      renderer: 'canvas' as const,
      useDirtyRect: false,
      width: 'auto' as const,
      height: 'auto' as const
    };

    const chart = echarts.init(container, theme, initOptions);

    // 添加全局错误处理
    chart.on('error', (error: any) => {
      console.error('ECharts 运行时错误:', error);
      // 尝试重置为基础图表
      try {
        const basicOption = getSafeBasicOption();
        chart.setOption(basicOption, { notMerge: true, silent: true });
      } catch (resetError) {
        console.error('重置图表失败:', resetError);
      }
    });

    // 添加其他事件监听
    chart.on('finished', () => {
      console.log('图表渲染完成');
    });

    // 重写 setOption 方法，自动修复颜色问题
    const originalSetOption = chart.setOption.bind(chart);
    chart.setOption = function(option: any, opts?: any) {
      try {
        const fixedOption = fixEChartsColorIssues(option);
        return originalSetOption(fixedOption, opts);
      } catch (error) {
        console.error('设置图表选项时发生错误:', error);
        // 尝试使用原始选项
        return originalSetOption(option, opts);
      }
    };

    return chart;
  } catch (error) {
    console.error('初始化 ECharts 失败:', error);
    return null;
  }
};

// 验证和清理图表选项
export const validateAndCleanOption = (option: any) => {
  try {
    if (!option || typeof option !== 'object') {
      console.warn('图表选项无效，返回默认选项');
      return getDefaultChartOption();
    }

    // 深拷贝选项以避免修改原始数据
    let cleanOption;
    try {
      cleanOption = JSON.parse(JSON.stringify(option));
    } catch (jsonError) {
      console.warn('图表选项序列化失败，返回默认选项');
      return getDefaultChartOption();
    }

    // 移除可能导致 barPolar 错误的配置
    // 删除极坐标相关配置，强制使用直角坐标系
    delete cleanOption.polar;
    delete cleanOption.angleAxis;
    delete cleanOption.radiusAxis;

    // 设置默认颜色调色板
    const defaultColors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'];
    if (!cleanOption.color || !Array.isArray(cleanOption.color)) {
      cleanOption.color = defaultColors;
    }

    // 确保 series 存在且为数组
    if (!cleanOption.series || !Array.isArray(cleanOption.series)) {
      cleanOption.series = [{
        name: '默认系列',
        type: 'bar',
        data: [],
        itemStyle: {
          color: defaultColors[0]
        }
      }];
    }

    // 过滤并清理每个 series 项
    cleanOption.series = cleanOption.series
      .filter((series: any) => series !== null && series !== undefined)
      .map((series: any, index: number) => {
        if (!series || typeof series !== 'object') {
          return {
            name: `系列${index + 1}`,
            type: 'bar',
            data: [],
            itemStyle: {
              color: cleanOption.color[index % cleanOption.color.length]
            }
          };
        }

        // 验证 series 类型，排除可能有问题的类型
        const validTypes = ['line', 'bar', 'pie', 'scatter', 'radar', 'gauge'];
        const seriesType = series.type && validTypes.includes(series.type) ? series.type : 'bar';

        // 获取当前系列的颜色
        const seriesColor = series.itemStyle?.color ||
                           series.lineStyle?.color ||
                           cleanOption.color[index % cleanOption.color.length];

        // 清理 series 数据
        const cleanSeries: any = {
          name: series.name || `系列${index + 1}`,
          type: seriesType,
          data: Array.isArray(series.data) ? series.data : [],
          // 确保每个 series 都有完整的颜色配置
          itemStyle: {
            color: seriesColor,
            borderColor: seriesColor,
            borderWidth: 0,
            ...series.itemStyle
          }
        };

        // 为线图添加 lineStyle
        if (seriesType === 'line') {
          cleanSeries.lineStyle = {
            color: seriesColor,
            width: 2,
            ...series.lineStyle
          };
        }

        // 为面积图添加 areaStyle
        if (seriesType === 'line' && series.areaStyle) {
          cleanSeries.areaStyle = {
            color: seriesColor,
            opacity: 0.3,
            ...series.areaStyle
          };
        }

        // 安全地复制基本属性，排除可能有问题的属性
        const safeKeys = ['barWidth', 'emphasis', 'label', 'smooth', 'symbol', 'symbolSize', 'stack', 'yAxisIndex', 'xAxisIndex'];
        safeKeys.forEach(key => {
          if (series[key] !== null && series[key] !== undefined) {
            try {
              cleanSeries[key] = series[key];
            } catch (error) {
              console.warn(`跳过无效的 series 属性: ${key}`);
            }
          }
        });

        // 确保不使用极坐标系
        delete cleanSeries.coordinateSystem;
        delete cleanSeries.polarIndex;

        return cleanSeries;
      });

    // 确保至少有一个有效的 series
    if (cleanOption.series.length === 0) {
      cleanOption.series = [{
        name: '默认系列',
        type: 'bar',
        data: [],
        itemStyle: {
          color: cleanOption.color[0]
        }
      }];
    }

    // 确保基本配置存在
    if (!cleanOption.xAxis) {
      cleanOption.xAxis = { type: 'category', data: [] };
    }
    if (!cleanOption.yAxis) {
      cleanOption.yAxis = { type: 'value' };
    }

    // 确保 tooltip 配置存在并修复颜色问题
    cleanOption.tooltip = {
      trigger: 'axis',
      confine: true,
      backgroundColor: 'rgba(255,255,255,0.9)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333',
        fontSize: 12
      },
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(150,150,150,0.3)'
        }
      },
      // 自定义 formatter 来确保颜色正确显示
      formatter: function(params: any) {
        try {
          if (!params) return '';

          if (Array.isArray(params)) {
            let result = (params[0]?.name || '未知') + '<br/>';
            params.forEach((param, index) => {
              // 安全获取颜色
              let color = '#5470c6'; // 默认颜色
              if (param.color) {
                color = param.color;
              } else if (cleanOption.color && cleanOption.color[index]) {
                color = cleanOption.color[index];
              } else if (param.seriesIndex !== undefined && cleanOption.series[param.seriesIndex]?.itemStyle?.color) {
                color = cleanOption.series[param.seriesIndex].itemStyle.color;
              }

              const marker = `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
              result += marker + (param.seriesName || '系列') + ': ' + (param.value !== undefined ? param.value : '无数据') + '<br/>';
            });
            return result;
          } else {
            // 单个参数的情况
            let color = params.color || cleanOption.color[0] || '#5470c6';
            const marker = `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
            return (params.name || '未知') + '<br/>' + marker + (params.seriesName || '系列') + ': ' + (params.value !== undefined ? params.value : '无数据');
          }
        } catch (error) {
          console.warn('Tooltip formatter 错误:', error);
          return '数据显示错误';
        }
      }
    };

    // 确保 grid 配置存在
    if (!cleanOption.grid) {
      cleanOption.grid = {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      };
    }

    return cleanOption;
  } catch (error) {
    console.error('清理图表选项失败:', error);
    return getDefaultChartOption();
  }
};

// 创建最安全的基础图表选项
export const getSafeBasicOption = () => {
  return {
    xAxis: {
      type: 'category',
      data: ['A', 'B', 'C', 'D', 'E']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: '基础数据',
      type: 'bar',
      data: [10, 20, 30, 40, 50]
    }]
  };
};

// 安全的设置选项方法
export const safeSetOption = (chart: any, option: any) => {
  try {
    if (!chart) {
      console.warn('图表实例无效');
      return false;
    }

    if (!option || typeof option !== 'object') {
      console.warn('图表选项无效，使用基础选项');
      const basicOption = getSafeBasicOption();
      chart.setOption(basicOption, { notMerge: true, silent: true });
      return true;
    }

    // 验证和清理选项
    const cleanOption = validateAndCleanOption(option);
    if (!cleanOption) {
      console.warn('图表选项验证失败，使用基础选项');
      const basicOption = getSafeBasicOption();
      chart.setOption(basicOption, { notMerge: true, silent: true });
      return true;
    }

    // 使用最安全的配置
    const setOptionConfig = {
      notMerge: true,  // 不合并，完全替换
      lazyUpdate: false,
      silent: true     // 静默模式，减少错误输出
    };

    chart.setOption(cleanOption, setOptionConfig);
    return true;
  } catch (error) {
    console.error('设置图表选项失败:', error);
    // 最后的降级方案
    try {
      const basicOption = getSafeBasicOption();
      chart.setOption(basicOption, { notMerge: true, silent: true });
      console.log('已降级到最基础的图表选项');
      return true;
    } catch (fallbackError) {
      console.error('设置基础图表选项也失败:', fallbackError);
      return false;
    }
  }
};

// 默认图表选项 - 简化版本，避免复杂配置
export const getDefaultChartOption = () => {
  try {
    return {
      title: {
        text: '默认图表',
        left: 'center',
        textStyle: {
          color: '#333',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255,255,255,0.9)',
        borderColor: '#ccc',
        borderWidth: 1,
        textStyle: {
          color: '#333',
          fontSize: 12
        }
      },
      grid: {
        left: '10%',
        right: '10%',
        top: '20%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月'],
        axisLine: {
          show: true,
          lineStyle: {
            color: '#ccc',
            width: 1
          }
        },
        axisLabel: {
          color: '#666',
          fontSize: 12
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: '#ccc'
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#ccc',
            width: 1
          }
        },
        axisLabel: {
          color: '#666',
          fontSize: 12
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            type: 'solid'
          }
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: '#ccc'
          }
        }
      },
      series: [{
        name: '数据系列',
        type: 'bar',
        data: [120, 200, 150, 80, 70, 110],
        barWidth: '60%',
        itemStyle: {
          color: '#409EFF'
        },
        emphasis: {
          itemStyle: {
            color: '#66b1ff'
          }
        },
        label: {
          show: false
        }
      }]
    };
  } catch (error) {
    console.error('创建默认图表选项失败:', error);
    // 返回最基本的选项
    return {
      xAxis: { type: 'category', data: ['A', 'B', 'C'] },
      yAxis: { type: 'value' },
      series: [{ name: '基础', type: 'bar', data: [1, 2, 3] }]
    };
  }
};

// 清理图表实例
export const disposeChart = (chart: any) => {
  try {
    if (chart && typeof chart.dispose === 'function') {
      chart.dispose();
    }
  } catch (error) {
    console.error('清理图表实例失败:', error);
  }
};

export default {
  configureECharts,
  safeInitECharts,
  safeSetOption,
  validateAndCleanOption,
  getDefaultChartOption,
  disposeChart
};
