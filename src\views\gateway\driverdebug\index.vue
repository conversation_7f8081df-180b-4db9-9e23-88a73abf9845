<template>
    <div class="driver-debug-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h2>驱动调试</h2>
        <p>调试和管理ThingsGateway的驱动插件</p>
      </div>
  
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧插件列表 -->
        <div class="plugin-list-panel">
          <div class="panel-header">
            <h3>支持调试的插件</h3>
            <el-button 
              type="primary" 
              size="small" 
              @click="refreshPlugins"
              :loading="loading"
            >
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          
          <div class="plugin-tree">
            <el-tree
              :data="pluginTreeData"
              :props="treeProps"
              node-key="fullName"
              :expand-on-click-node="false"
              @node-click="handlePluginClick"
              :default-expanded-keys="expandedKeys"
            >
              <template #default="{ data }">
                <div class="plugin-node">
                  <el-icon v-if="data.children">
                    <Folder />
                  </el-icon>
                  <el-icon v-else>
                    <Connection />
                  </el-icon>
                  <span class="plugin-name">{{ data.name }}</span>
                  <el-tag 
                    v-if="!data.children && data.hasDebugUI" 
                    size="small" 
                    type="success"
                  >
                    可调试
                  </el-tag>
                </div>
              </template>
            </el-tree>
          </div>
        </div>
  
        <!-- 右侧调试面板 -->
        <div class="debug-panel">
          <div v-if="selectedPlugin" class="debug-content">
            <!-- 插件信息 -->
            <div class="plugin-info">
              <h3>{{ selectedPlugin.name }}</h3>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="插件名称">
                  {{ selectedPlugin.name }}
                </el-descriptions-item>
                <el-descriptions-item label="文件名">
                  {{ selectedPlugin.fileName }}
                </el-descriptions-item>
                <el-descriptions-item label="类型名">
                  {{ selectedPlugin.typeName }}
                </el-descriptions-item>
                <el-descriptions-item label="版本">
                  {{ selectedPlugin.version }}
                </el-descriptions-item>
                <el-descriptions-item label="描述" :span="2">
                  {{ selectedPlugin.description || '暂无描述' }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
  
            <!-- 调试功能标签页 -->
            <div class="debug-tabs">
              <el-tabs v-model="activeTab" type="card">
                <!-- 属性配置 -->
                <el-tab-pane label="属性配置" name="properties">
                                     <PluginProperties 
                     :plugin-name="selectedPlugin.fullName"
                     :properties="pluginProperties.properties"
                     :editor-items="pluginProperties.editorItems"
                     @update="handlePropertiesUpdate"
                   />
                </el-tab-pane>
  
                <!-- 方法调试 -->
                <el-tab-pane label="方法调试" name="methods">
                  <PluginMethods 
                    :plugin-name="selectedPlugin.fullName"
                    :methods="pluginMethods"
                    @execute="handleMethodExecute"
                  />
                </el-tab-pane>
  
                                                 <!-- 调试UI -->
                <el-tab-pane label="调试UI" name="debugUI">
                  <DebugUIComponent 
                    v-if="debugUIInfo"
                    :plugin-name="selectedPlugin.fullName"
                    :debug-ui-info="debugUIInfo"
                  />
                </el-tab-pane>

                <!-- 地址配置 -->
                <el-tab-pane label="地址配置" name="address">
                  <AddressConfigComponent 
                    v-if="debugUIInfo"
                    :plugin-name="selectedPlugin.fullName"
                    :address-ui-info="debugUIInfo"
                  />
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
  
          <!-- 未选择插件时的提示 -->
          <div v-else class="no-selection">
            <el-empty description="请选择一个插件进行调试">
              <el-icon size="60"><Connection /></el-icon>
            </el-empty>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, onMounted, computed } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Refresh, Folder, Connection } from '@element-plus/icons-vue'
  import {
    getDebugPlugins,
    getDebugUIInfo,
    getPluginProperties,
    getPluginMethods,
    type DebugPluginInfo,
    type DebugUIInfo,
    type PluginPropertiesInfo
  } from '@/api/gateway/driverdebug'
  import PluginProperties from '@/views/components/driverdebug/PluginProperties.vue'
  import PluginMethods from '@/views/components/driverdebug/PluginMethods.vue'
  import DebugUI from '@/views/components/driverdebug/DebugUI.vue'
  import AddressConfig from '@/views/components/driverdebug/AddressConfig.vue'
  
  // 类型断言，解决TypeScript编译错误
  const DebugUIComponent = DebugUI as any
  const AddressConfigComponent = AddressConfig as any
  
  // 响应式数据
  const loading = ref(false)
  const plugins = ref<DebugPluginInfo[]>([])
  const selectedPlugin = ref<DebugPluginInfo | null>(null)
  const activeTab = ref('properties')
  const expandedKeys = ref<string[]>([])
  
  // 插件相关数据
  const pluginProperties = ref<PluginPropertiesInfo>({
    pluginName: '',
    properties: {},
    editorItems: [],
    propertyUITypeName: '',
    errorMessage: ''
  })
  const debugUIInfo = ref<DebugUIInfo>({
    pluginName: '',
    hasDebugUI: false,
    debugUITypeName: '',
    hasAddressUI: false,
    addressUITypeName: '',
    errorMessage: ''
  })
  const pluginMethods = ref<any[]>([])
  
  // 树形配置
  const treeProps = {
    children: 'children',
    label: 'name'
  }
  
  // 计算属性：构建树形数据
  const pluginTreeData = computed(() => {
    const groups = plugins.value.reduce((acc, plugin) => {
      const fileName = plugin.fileName
      if (!acc[fileName]) {
        acc[fileName] = {
          name: fileName,
          children: []
        }
      }
      acc[fileName].children.push(plugin)
      return acc
    }, {} as Record<string, any>)
  
    return Object.values(groups)
  })
  
  // 方法
  const refreshPlugins = async () => {
    loading.value = true
    try {
      const response = await getDebugPlugins()
      plugins.value = response.data || []
      expandedKeys.value = Object.keys(pluginTreeData.value.reduce((acc, group) => {
        acc[group.name] = group.name
        return acc
      }, {} as Record<string, string>))
      ElMessage.success('插件列表刷新成功')
    } catch (error: any) {
      console.error('获取插件列表失败:', error)
      if (error.response?.status === 404) {
        ElMessage.error('API接口不存在，请联系后端开发人员实现插件列表接口')
      } else {
        ElMessage.error('获取插件列表失败')
      }
    } finally {
      loading.value = false
    }
  }
  
  const handlePluginClick = async (data: any) => {
    if (data.children) return // 点击的是分组节点
  
    selectedPlugin.value = data
    activeTab.value = 'properties'
    
    // 加载插件详细信息
    await loadPluginDetails(data.fullName)
  }
  
    const loadPluginDetails = async (pluginName: string) => {
    try {
      // 并行加载插件信息
      const [propertiesRes, uiInfoRes, methodsRes] = await Promise.all([
        getPluginProperties(pluginName),
        getDebugUIInfo(pluginName),
        getPluginMethods(pluginName)
      ])

      // 安全地设置数据，确保数据格式正确
      if (propertiesRes && propertiesRes.data) {
        pluginProperties.value = propertiesRes.data
      }
      if (uiInfoRes && uiInfoRes.data) {
        debugUIInfo.value = uiInfoRes.data
      }
      if (methodsRes && methodsRes.data && Array.isArray(methodsRes.data)) {
        pluginMethods.value = methodsRes.data
      } else {
        pluginMethods.value = []
      }
    } catch (error) {
      console.error('加载插件详情失败:', error)
      ElMessage.error('加载插件详情失败')
      throw error
    }
  }
  
  const handlePropertiesUpdate = (properties: Record<string, any>) => {
    ElMessage.success('属性配置已更新')
    console.log('属性配置更新:', properties)
  }
  
  const handleMethodExecute = async (methodInfo: any) => {
    try {
      ElMessage.success(`方法 ${methodInfo.name} 执行成功`)
    } catch (error) {
      console.error('方法执行失败:', error)
      ElMessage.error('方法执行失败')
    }
  }
  
  // 生命周期
  onMounted(() => {
    refreshPlugins()
  })
  </script>
  
  <style scoped>
  .driver-debug-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
  }
  
  .page-header {
    background: white;
    padding: 20px;
    border-bottom: 1px solid #e4e7ed;
  }
  
  .page-header h2 {
    margin: 0 0 8px 0;
    color: #303133;
  }
  
  .page-header p {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }
  
  .main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
  }
  
  .plugin-list-panel {
    width: 300px;
    background: white;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
  }
  
  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .panel-header h3 {
    margin: 0;
    font-size: 16px;
    color: #303133;
  }
  
  .plugin-tree {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
  }
  
  .plugin-node {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .plugin-name {
    flex: 1;
  }
  
  .debug-panel {
    flex: 1;
    background: white;
    display: flex;
    flex-direction: column;
  }
  
  .debug-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }
  
  .plugin-info {
    margin-bottom: 20px;
  }
  
  .plugin-info h3 {
    margin: 0 0 16px 0;
    color: #303133;
  }
  
  .debug-tabs {
    flex: 1;
  }
  
  .no-selection {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  :deep(.el-tree-node__content) {
    height: 40px;
  }
  
  :deep(.el-tree-node__label) {
    flex: 1;
  }
  
  :deep(.el-descriptions__label) {
    font-weight: 600;
  }
  </style> 