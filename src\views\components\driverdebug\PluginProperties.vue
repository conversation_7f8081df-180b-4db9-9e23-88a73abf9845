<template>
    <div class="plugin-properties">
      <div class="properties-header">
        <h4>插件属性配置</h4>
        <el-button type="primary" size="small" @click="saveProperties">
          保存配置
        </el-button>
      </div>
  
      <div v-if="editorItems && Array.isArray(editorItems) && editorItems.length > 0" class="properties-form">
        <el-form 
          ref="formRef" 
          :model="formData" 
          :rules="formRules" 
          label-width="120px"
        >
          <el-form-item 
            v-for="item in editorItems" 
            :key="item.field"
            :label="item.text"
            :prop="item.field"
          >
            <!-- 字符串类型 -->
            <el-input 
              v-if="item.type === 'String'"
              v-model="formData[item.field]"
              :placeholder="`请输入${item.text}`"
              :required="item.isRequired"
            />
  
            <!-- 数字类型 -->
            <el-input-number 
              v-else-if="item.type === 'Int32' || item.type === 'Int64' || item.type === 'Double'"
              v-model="formData[item.field]"
              :placeholder="`请输入${item.text}`"
              :required="item.isRequired"
              style="width: 100%"
            />
  
            <!-- 布尔类型 -->
            <el-switch 
              v-else-if="item.type === 'Boolean'"
              v-model="formData[item.field]"
              :active-text="item.text"
            />
  
            <!-- 枚举类型 -->
            <el-select 
              v-else-if="item.type.includes('Enum')"
              v-model="formData[item.field]"
              :placeholder="`请选择${item.text}`"
              style="width: 100%"
            >
              <el-option 
                v-for="option in getEnumOptions(item.type)"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
  
            <!-- 默认文本输入 -->
            <el-input 
              v-else
              v-model="formData[item.field]"
              :placeholder="`请输入${item.text}`"
              :required="item.isRequired"
            />
          </el-form-item>
        </el-form>
      </div>
  
      <div v-else class="no-properties">
        <el-empty description="该插件暂无属性配置">
          <el-icon size="40"><Setting /></el-icon>
        </el-empty>
      </div>
  
      <!-- 属性预览 -->
      <div v-if="properties && typeof properties === 'object' && Object.keys(properties).length > 0" class="properties-preview">
        <h5>当前属性值</h5>
        <el-descriptions :column="2" border size="small">
          <el-descriptions-item 
            v-for="(value, key) in properties" 
            :key="key"
            :label="key"
          >
            {{ value }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, onMounted, watch } from 'vue'
  import { ElMessage, ElForm } from 'element-plus'
  import { Setting } from '@element-plus/icons-vue'
  import type { EditorItemInfo } from '@/api/gateway/driverdebug'
  
  // Props
  interface Props {
    pluginName: string
    properties: Record<string, string>
    editorItems: EditorItemInfo[]
  }
  
  const props = defineProps<Props>()
  
  // Emits
  const emit = defineEmits<{
    update: [properties: Record<string, any>]
  }>()
  
  // 响应式数据
  const formRef = ref<InstanceType<typeof ElForm>>()
  const formData = reactive<Record<string, any>>({})
  const formRules = reactive<Record<string, any>>({})
  
    // 初始化表单数据
  const initFormData = () => {
    // 安全地清空表单数据
    const formDataKeys = Object.keys(formData)
    formDataKeys.forEach(key => {
      delete formData[key]
    })

    // 安全地清空表单规则
    const formRulesKeys = Object.keys(formRules)
    formRulesKeys.forEach(key => {
      delete formRules[key]
    })

    // 确保editorItems存在且是数组
    if (!props.editorItems || !Array.isArray(props.editorItems)) {
      return
    }

    // 根据编辑器项初始化表单
    props.editorItems.forEach(item => {
      if (!item || !item.field) return
      
      // 设置默认值
      if (item.defaultValue !== undefined && item.defaultValue !== null) {
        formData[item.field] = parseValue(item.defaultValue, item.type)
      } else {
        formData[item.field] = getDefaultValue(item.type)
      }

      // 设置验证规则
      if (item.isRequired) {
        formRules[item.field] = [
          { required: true, message: `${item.text || item.field}不能为空`, trigger: 'blur' }
        ]
      }
    })

    // 从properties中加载现有值
    if (props.properties && typeof props.properties === 'object') {
      Object.keys(props.properties).forEach(key => {
        if (formData.hasOwnProperty(key)) {
          formData[key] = props.properties[key]
        }
      })
    }
  }
  
  // 解析值
  const parseValue = (value: string, type: string): any => {
    switch (type) {
      case 'Boolean':
        return value.toLowerCase() === 'true'
      case 'Int32':
      case 'Int64':
        return parseInt(value) || 0
      case 'Double':
        return parseFloat(value) || 0
      default:
        return value
    }
  }
  
  // 获取默认值
  const getDefaultValue = (type: string): any => {
    switch (type) {
      case 'Boolean':
        return false
      case 'Int32':
      case 'Int64':
      case 'Double':
        return 0
      default:
        return ''
    }
  }
  
  // 获取枚举选项
  const getEnumOptions = (enumType: string): Array<{value: string, label: string}> => {
    // 这里可以根据枚举类型返回对应的选项
    // 实际项目中可能需要从后端获取枚举定义
    return []
  }
  
  // 保存属性配置
  const saveProperties = async () => {
    if (!formRef.value) return
  
    try {
      await formRef.value.validate()
      
      // 转换数据格式
      const properties: Record<string, any> = {}
      Object.keys(formData).forEach(key => {
        properties[key] = formData[key]
      })
  
      emit('update', properties)
      ElMessage.success('属性配置保存成功')
    } catch (error) {
      console.error('表单验证失败:', error)
      ElMessage.error('请检查表单输入')
    }
  }
  
    // 监听props变化
  watch(() => props.editorItems, () => {
    try {
      initFormData()
    } catch (error) {
      console.warn('初始化表单数据失败:', error)
    }
  }, { immediate: true })

  watch(() => props.properties, () => {
    try {
      // 更新现有属性值
      if (props.properties && typeof props.properties === 'object') {
        Object.keys(props.properties).forEach(key => {
          if (formData.hasOwnProperty(key)) {
            formData[key] = props.properties[key]
          }
        })
      }
    } catch (error) {
      console.warn('更新属性值失败:', error)
    }
  }, { deep: true })
  
  // 生命周期
  onMounted(() => {
    initFormData()
  })
  
  </script>

<script lang="ts">
export default {
  name: 'PluginProperties'
}
</script>
  
  <style scoped>
  .plugin-properties {
    padding: 16px;
  }
  
  .properties-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .properties-header h4 {
    margin: 0;
    color: #303133;
  }
  
  .properties-form {
    margin-bottom: 20px;
  }
  
  .no-properties {
    text-align: center;
    padding: 40px 0;
  }
  
  .properties-preview {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;
  }
  
  .properties-preview h5 {
    margin: 0 0 16px 0;
    color: #606266;
    font-size: 14px;
  }
  
  :deep(.el-form-item__label) {
    font-weight: 500;
  }
  
  :deep(.el-input-number) {
    width: 100%;
  }
  </style> 