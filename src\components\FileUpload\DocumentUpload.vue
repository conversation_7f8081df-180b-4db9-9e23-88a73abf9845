<template>
  <div class="document-upload-container">
    <!-- 拖拽上传区域 -->
    <div
      class="upload-area"
      :class="{
        'drag-over': isDragOver,
        'uploading': isUploading,
        'disabled': disabled
      }"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      @click="triggerFileSelect"
    >
      <input
        ref="fileInput"
        type="file"
        multiple
        :accept="acceptedTypes"
        @change="handleFileSelect"
        style="display: none"
      />
      
      <div class="upload-content">
        <div v-if="!isUploading" class="upload-icon">
          <el-icon :size="48" color="#409EFF">
            <Upload />
          </el-icon>
        </div>
        
        <div v-if="isUploading" class="upload-progress-icon">
          <el-icon :size="48" color="#67C23A" class="rotating">
            <Loading />
          </el-icon>
        </div>
        
        <div class="upload-text">
          <h3 v-if="!isUploading">{{ uploadTitle }}</h3>
          <h3 v-else>正在上传文件...</h3>
          <p class="upload-hint">
            {{ uploadHint }}
          </p>
        </div>
        
        <div v-if="!isUploading" class="upload-button">
          <el-button type="primary" :disabled="disabled">
            选择文件
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 文件列表 -->
    <div v-if="fileList.length > 0" class="file-list">
      <h4>待上传文件 ({{ fileList.length }})</h4>
      <div class="file-items">
        <div
          v-for="(file, index) in fileList"
          :key="index"
          class="file-item"
          :class="{ 'upload-error': file.status === 'error' }"
        >
          <div class="file-info">
            <div class="file-icon">
              <el-icon :size="24" :color="getFileIconColor(file.type)">
                <Document />
              </el-icon>
            </div>
            <div class="file-details">
              <div class="file-name">{{ file.name }}</div>
              <div class="file-meta">
                {{ formatFileSize(file.size) }} • {{ getFileTypeLabel(file.type) }}
              </div>
            </div>
          </div>
          
          <div class="file-actions">
            <div v-if="file.status === 'uploading'" class="upload-progress">
              <el-progress
                :percentage="file.progress"
                :stroke-width="4"
                :show-text="false"
              />
              <span class="progress-text">{{ file.progress }}%</span>
            </div>
            
            <div v-else-if="file.status === 'success'" class="upload-success">
              <el-icon color="#67C23A">
                <Check />
              </el-icon>
              <span>上传成功</span>
            </div>
            
            <div v-else-if="file.status === 'error'" class="upload-error">
              <el-icon color="#F56C6C">
                <Close />
              </el-icon>
              <span>{{ file.error || '上传失败' }}</span>
            </div>
            
            <el-button
              v-if="file.status !== 'uploading'"
              type="text"
              size="small"
              @click="removeFile(index)"
            >
              移除
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 批量操作 -->
      <div class="batch-actions">
        <el-button
          type="primary"
          :loading="isUploading"
          :disabled="!canUpload"
          @click="startUpload"
        >
          开始上传 ({{ pendingFiles.length }})
        </el-button>
        <el-button
          :disabled="isUploading"
          @click="clearAll"
        >
          清空列表
        </el-button>
      </div>
    </div>
    
    <!-- 上传配置 -->
    <div v-if="showConfig" class="upload-config">
      <el-form :model="uploadConfig" label-width="120px" size="small">
        <el-form-item label="解析器">
          <el-select v-model="uploadConfig.parserId" style="width: 200px">
            <el-option
              v-for="parser in parserOptions"
              :key="parser.value"
              :label="parser.label"
              :value="parser.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="上传后解析">
          <el-switch v-model="uploadConfig.runAfterUpload" />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { Upload, Loading, Document, Check, Close } from '@element-plus/icons-vue';
import { uploadDocument as uploadDocumentAPI } from '/@/api/iot/document';

// Props
interface Props {
  knowledgeBaseId: string;
  disabled?: boolean;
  showConfig?: boolean;
  maxFileSize?: number; // MB
  acceptedTypes?: string;
  uploadTitle?: string;
  uploadHint?: string;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  showConfig: true,
  maxFileSize: 100,
  acceptedTypes: '.pdf,.docx,.doc,.txt,.md,.html,.xlsx,.xls,.ppt,.pptx,.csv,.json,.xml',
  uploadTitle: '拖拽文件到此处或点击上传',
  uploadHint: '支持 PDF、Word、Excel、TXT、Markdown 等格式，单个文件最大 100MB'
});

// Emits
const emit = defineEmits<{
  uploadSuccess: [files: any[]];
  uploadError: [error: string];
  uploadProgress: [progress: number];
}>();

// 响应式数据
const fileInput = ref<HTMLInputElement>();
const isDragOver = ref(false);
const isUploading = ref(false);
const fileList = ref<any[]>([]);

// 上传配置
const uploadConfig = reactive({
  parserId: 'naive',
  runAfterUpload: true
});

// 解析器选项
const parserOptions = [
  { label: '通用', value: 'naive' },
  { label: '书籍', value: 'book' },
  { label: '论文', value: 'paper' },
  { label: '演示文稿', value: 'presentation' },
  { label: '表格', value: 'table' }
];

// 计算属性
const pendingFiles = computed(() => 
  fileList.value.filter(file => file.status === 'pending')
);

const canUpload = computed(() => 
  pendingFiles.value.length > 0 && !isUploading.value && !props.disabled
);

// 拖拽事件处理
const handleDragOver = (e: DragEvent) => {
  e.preventDefault();
  e.stopPropagation();
};

const handleDragEnter = (e: DragEvent) => {
  e.preventDefault();
  e.stopPropagation();
  isDragOver.value = true;
};

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault();
  e.stopPropagation();
  isDragOver.value = false;
};

const handleDrop = (e: DragEvent) => {
  e.preventDefault();
  e.stopPropagation();
  isDragOver.value = false;
  
  if (props.disabled) return;
  
  const files = Array.from(e.dataTransfer?.files || []);
  addFiles(files);
};

// 文件选择处理
const triggerFileSelect = () => {
  if (!props.disabled && fileInput.value) {
    fileInput.value.click();
  }
};

const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement;
  const files = Array.from(target.files || []);
  addFiles(files);
  target.value = ''; // 清空input，允许重复选择同一文件
};

// 添加文件到列表
const addFiles = (files: File[]) => {
  for (const file of files) {
    if (validateFile(file)) {
      const fileItem = {
        name: file.name,
        size: file.size,
        type: file.type,
        file: file,
        status: 'pending',
        progress: 0,
        error: null,
        uploadId: null
      };
      fileList.value.push(fileItem);
    }
  }
};

// 文件验证
const validateFile = (file: File): boolean => {
  // 检查文件大小
  const maxSize = props.maxFileSize * 1024 * 1024; // 转换为字节
  if (file.size > maxSize) {
    ElMessage.error(`文件 ${file.name} 大小超过 ${props.maxFileSize}MB 限制`);
    return false;
  }
  
  // 检查文件类型
  const fileExt = '.' + file.name.split('.').pop()?.toLowerCase();
  if (!props.acceptedTypes.includes(fileExt)) {
    ElMessage.error(`不支持的文件类型: ${fileExt}`);
    return false;
  }
  
  return true;
};

// 防重复触发标记
let uploadEventEmitted = false;

// 开始上传
const startUpload = async () => {
  if (!canUpload.value) return;

  isUploading.value = true;
  uploadEventEmitted = false; // 重置标记

  try {
    for (const fileItem of pendingFiles.value) {
      await uploadSingleFile(fileItem);
    }

    const successFiles = fileList.value.filter(f => f.status === 'success');
    if (successFiles.length > 0 && !uploadEventEmitted) {
      console.log('DocumentUpload emitting uploadSuccess with files:', successFiles);
      uploadEventEmitted = true; // 设置标记防止重复触发
      emit('uploadSuccess', successFiles);
      // 移除这里的成功消息，由父组件统一处理
    }
  } catch (error) {
    emit('uploadError', error as string);
  } finally {
    isUploading.value = false;
  }
};

// 上传单个文件
const uploadSingleFile = async (fileItem: any) => {
  fileItem.status = 'uploading';
  fileItem.progress = 0;

  try {
    // 调用实际的上传API，暂时使用简单的进度模拟
    // TODO: 后续可以改进为真实的上传进度
    const progressInterval = setInterval(() => {
      if (fileItem.progress < 90) {
        fileItem.progress += 10;
      }
    }, 200);

    const result = await uploadDocumentAPI(props.knowledgeBaseId, fileItem.file, {
      parser_id: uploadConfig.parserId,
      run_after_upload: uploadConfig.runAfterUpload
    });

    clearInterval(progressInterval);

    if (result && result.data && result.data.code === 200) {
      fileItem.status = 'success';
      fileItem.progress = 100;
      fileItem.result = result.data.data; // 保存上传结果
      console.log('Upload success:', result.data);
    } else {
      throw new Error(result?.data?.message || '上传失败');
    }
  } catch (error: any) {
    fileItem.status = 'error';
    fileItem.progress = 0;

    // 处理不同类型的错误
    let errorMessage = '上传失败';
    if (error.response) {
      // 服务器响应错误
      const detail = error.response.data?.detail || error.response.data?.message;
      if (error.response.status === 504 && detail?.includes('上传超时')) {
        // 特殊处理上传超时
        errorMessage = '文件上传超时，但可能已在后台处理中，请稍后刷新文档列表查看状态';
        ElMessage.warning(`${fileItem.file.name}: ${errorMessage}`);
      } else {
        errorMessage = detail || `服务器错误 (${error.response.status})`;
        ElMessage.error(`${fileItem.file.name}: ${errorMessage}`);
      }
    } else if (error.request) {
      // 网络错误
      errorMessage = '网络连接失败，请检查网络状态';
      ElMessage.error(`${fileItem.file.name}: ${errorMessage}`);
    } else {
      // 其他错误
      errorMessage = error.message || '未知错误';
      ElMessage.error(`${fileItem.file.name}: ${errorMessage}`);
    }

    fileItem.error = errorMessage;
    console.error('Upload error:', error);
  }
};

// 移除文件
const removeFile = (index: number) => {
  fileList.value.splice(index, 1);
};

// 清空列表
const clearAll = () => {
  fileList.value = [];
};

// 工具函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const getFileTypeLabel = (mimeType: string): string => {
  const typeMap: Record<string, string> = {
    'application/pdf': 'PDF',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word',
    'application/msword': 'Word',
    'text/plain': 'TXT',
    'text/markdown': 'Markdown',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',
    'application/vnd.ms-excel': 'Excel'
  };
  return typeMap[mimeType] || '文档';
};

const getFileIconColor = (mimeType: string): string => {
  if (mimeType.includes('pdf')) return '#F56C6C';
  if (mimeType.includes('word')) return '#409EFF';
  if (mimeType.includes('sheet') || mimeType.includes('excel')) return '#67C23A';
  if (mimeType.includes('text')) return '#909399';
  return '#409EFF';
};
</script>

<style scoped>
.document-upload-container {
  width: 100%;
}

.upload-area {
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
}

.upload-area:hover {
  border-color: #409EFF;
  background-color: #f0f9ff;
}

.upload-area.drag-over {
  border-color: #409EFF;
  background-color: #e6f7ff;
  transform: scale(1.02);
}

.upload-area.uploading {
  border-color: #67C23A;
  background-color: #f0f9f0;
  cursor: not-allowed;
}

.upload-area.disabled {
  border-color: #e4e7ed;
  background-color: #f5f7fa;
  cursor: not-allowed;
  opacity: 0.6;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.upload-icon, .upload-progress-icon {
  margin-bottom: 8px;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.upload-text h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 500;
}

.upload-hint {
  margin: 0;
  color: #909399;
  font-size: 14px;
  line-height: 1.5;
}

.file-list {
  margin-top: 24px;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

.file-list h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.file-items {
  margin-bottom: 20px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item.upload-error {
  background-color: #fef0f0;
  padding: 12px;
  border-radius: 4px;
  border-bottom: 1px solid #f0f0f0;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 12px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.file-meta {
  font-size: 12px;
  color: #909399;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.upload-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  min-width: 30px;
}

.upload-success, .upload-error {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.upload-success {
  color: #67C23A;
}

.upload-error {
  color: #F56C6C;
}

.batch-actions {
  display: flex;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.upload-config {
  margin-top: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}
</style>
