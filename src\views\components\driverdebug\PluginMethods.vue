<template>
    <div class="plugin-methods">
      <div class="methods-header">
        <h4>插件方法调试</h4>
        <el-button type="primary" size="small" @click="refreshMethods">
          刷新方法
        </el-button>
      </div>
  
      <div v-if="methods.length > 0" class="methods-list">
        <el-card 
          v-for="method in methods" 
          :key="method.name"
          class="method-card"
          shadow="hover"
        >
          <template #header>
            <div class="method-header">
              <span class="method-name">{{ method.name }}</span>
              <el-tag 
                :type="getMethodTypeColor(method.type)"
                size="small"
              >
                {{ method.type }}
              </el-tag>
            </div>
          </template>
  
          <div class="method-content">
            <p class="method-description">{{ method.description || '暂无描述' }}</p>
            
            <!-- 参数配置 -->
            <div v-if="method.parameters && Array.isArray(method.parameters) && method.parameters.length > 0" class="method-params">
              <h5>参数配置</h5>
              <el-form :model="methodParams[method.name]" label-width="80px">
                <el-form-item 
                  v-for="param in method.parameters"
                  :key="param.name || param.field || 'param'"
                  :label="param.name || param.field || '参数'"
                >
                  <el-input 
                    v-model="methodParams[method.name][param.name || param.field || 'param']"
                    :placeholder="`请输入${param.name || param.field || '参数'}`"
                    size="small"
                  />
                </el-form-item>
              </el-form>
            </div>
  
            <!-- 执行按钮 -->
            <div class="method-actions">
              <el-button 
                type="primary" 
                size="small"
                @click="executeMethod(method)"
                :loading="executingMethods[method.name]"
              >
                执行方法
              </el-button>
            </div>
  
            <!-- 执行结果 -->
            <div v-if="methodResults[method.name]" class="method-result">
              <h6>执行结果</h6>
              <el-alert
                :title="methodResults[method.name].success ? '执行成功' : '执行失败'"
                :type="methodResults[method.name].success ? 'success' : 'error'"
                :description="methodResults[method.name].result || methodResults[method.name].errorMessage"
                show-icon
                :closable="false"
              />
            </div>
          </div>
        </el-card>
      </div>
  
      <div v-else class="no-methods">
        <el-empty description="该插件暂无可用方法">
          <el-icon size="40"><Operation /></el-icon>
        </el-empty>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, onMounted, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Operation } from '@element-plus/icons-vue'
  import { executeMethod as executeMethodApi } from '@/api/gateway/driverdebug'
  
  // Props
  interface Props {
    pluginName: string
    methods: any[]
  }
  
  const props = defineProps<Props>()
  
  // Emits
  const emit = defineEmits<{
    execute: [methodInfo: any]
  }>()
  
  // 响应式数据
  const methodParams = reactive<Record<string, Record<string, any>>>({})
  const methodResults = reactive<Record<string, any>>({})
  const executingMethods = reactive<Record<string, boolean>>({})
  
  // 初始化方法参数
  const initMethodParams = () => {
    props.methods.forEach(method => {
      if (!methodParams[method.name]) {
        methodParams[method.name] = {}
      }
      
      if (method.parameters && Array.isArray(method.parameters)) {
        method.parameters.forEach((param: any) => {
          const paramName = param.name || param.field || 'param'
          if (!methodParams[method.name][paramName]) {
            methodParams[method.name][paramName] = ''
          }
        })
      }
    })
  }
  
  // 获取方法类型颜色
  const getMethodTypeColor = (type: string) => {
    switch (type) {
      case 'Read':
        return 'success'
      case 'Write':
        return 'warning'
      case 'Control':
        return 'danger'
      default:
        return 'info'
    }
  }
  
  // 刷新方法
  const refreshMethods = () => {
    ElMessage.success('方法列表已刷新')
    initMethodParams()
  }
  
  // 执行方法
  const executeMethod = async (method: any) => {
    executingMethods[method.name] = true
    
    try {
      const parameters = methodParams[method.name] || {}
      
      const response = await executeMethodApi({
        pluginName: props.pluginName,
        methodName: method.name,
        parameters
      })
  
      methodResults[method.name] = response.data
      emit('execute', method)
      
      if (response.data.success) {
        ElMessage.success(`方法 ${method.name} 执行成功`)
      } else {
        ElMessage.error(`方法 ${method.name} 执行失败: ${response.data.errorMessage}`)
      }
    } catch (error) {
      console.error('方法执行失败:', error)
      methodResults[method.name] = {
        success: false,
        errorMessage: '执行失败，请检查网络连接'
      }
      ElMessage.error('方法执行失败')
    } finally {
      executingMethods[method.name] = false
    }
  }
  
  // 监听methods变化
  watch(() => props.methods, () => {
    initMethodParams()
  }, { immediate: true })
  
  // 生命周期
  onMounted(() => {
    initMethodParams()
  })
  
  </script>

<script lang="ts">
export default {
  name: 'PluginMethods'
}
</script>
  
  <style scoped>
  .plugin-methods {
    padding: 16px;
  }
  
  .methods-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .methods-header h4 {
    margin: 0;
    color: #303133;
  }
  
  .methods-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .method-card {
    border: 1px solid #e4e7ed;
  }
  
  .method-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .method-name {
    font-weight: 600;
    color: #303133;
  }
  
  .method-content {
    padding: 8px 0;
  }
  
  .method-description {
    margin: 0 0 16px 0;
    color: #606266;
    font-size: 14px;
  }
  
  .method-params {
    margin-bottom: 16px;
  }
  
  .method-params h5 {
    margin: 0 0 12px 0;
    color: #303133;
    font-size: 14px;
  }
  
  .method-actions {
    margin-bottom: 16px;
  }
  
  .method-result {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
  }
  
  .method-result h6 {
    margin: 0 0 8px 0;
    color: #606266;
    font-size: 13px;
  }
  
  .no-methods {
    text-align: center;
    padding: 40px 0;
  }
  
  :deep(.el-card__header) {
    padding: 12px 16px;
    background-color: #fafafa;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
  
  :deep(.el-form-item__label) {
    font-size: 13px;
  }
  </style> 