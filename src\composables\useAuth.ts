import { computed } from 'vue';
import { useUserInfo } from '/@/stores/userInfo';

/**
 * 权限控制组合函数
 */
export function useAuth() {
  const userInfoStore = useUserInfo();

  /**
   * 检查用户是否拥有指定权限
   * @param permission 权限标识，支持字符串或数组
   * @returns 是否拥有权限
   */
  const hasPermission = (permission: string | string[]): boolean => {
    if (!permission) return true;
    
    const userPermissions = userInfoStore.userInfos?.permissions || [];
    
    if (typeof permission === 'string') {
      return checkSinglePermission(permission, userPermissions);
    }
    
    if (Array.isArray(permission)) {
      return permission.some(p => checkSinglePermission(p, userPermissions));
    }
    
    return false;
  };

  /**
   * 检查单个权限
   * @param permission 权限标识
   * @param userPermissions 用户权限列表
   * @returns 是否拥有权限
   */
  const checkSinglePermission = (permission: string, userPermissions: string[]): boolean => {
    // 超级管理员权限
    if (userPermissions.includes('*:*:*')) {
      return true;
    }
    
    // 直接匹配
    if (userPermissions.includes(permission)) {
      return true;
    }
    
    // 通配符匹配
    return userPermissions.some(userPerm => {
      if (userPerm.includes('*')) {
        const pattern = userPerm.replace(/\*/g, '.*');
        const regex = new RegExp(`^${pattern}$`);
        return regex.test(permission);
      }
      return false;
    });
  };

  /**
   * 检查知识库相关权限
   */
  const kbPermissions = computed(() => ({
    canList: hasPermission('knowledge:base:list'),
    canView: hasPermission('knowledge:base:view'),
    canCreate: hasPermission('knowledge:base:create'),
    canEdit: hasPermission('knowledge:base:update'),
    canDelete: hasPermission('knowledge:base:delete'),
    canManage: hasPermission('knowledge:base:manage'),
    canStats: hasPermission('knowledge:base:stats'),
  }));

  /**
   * 检查是否拥有知识库管理权限
   */
  const hasKbManagePermission = computed(() => {
    return hasPermission([
      'knowledge:base:manage',
      'knowledge:base:list',
      'knowledge:base:view',
      'knowledge:base:create',
      'knowledge:base:update',
      'knowledge:base:delete'
    ]);
  });

  return {
    hasPermission,
    kbPermissions,
    hasKbManagePermission
  };
}

/**
 * 权限指令
 * 用法：v-auth="'iot:kb:create'"
 */
export const authDirective = {
  mounted(el: HTMLElement, binding: any) {
    const { hasPermission } = useAuth();
    const permission = binding.value;
    
    if (!hasPermission(permission)) {
      el.style.display = 'none';
      // 或者移除元素
      // el.parentNode?.removeChild(el);
    }
  },
  updated(el: HTMLElement, binding: any) {
    const { hasPermission } = useAuth();
    const permission = binding.value;
    
    if (!hasPermission(permission)) {
      el.style.display = 'none';
    } else {
      el.style.display = '';
    }
  }
};

/**
 * 权限检查工具函数
 */
export const authUtils = {
  /**
   * 检查知识库操作权限
   */
  checkKbPermission: (action: 'list' | 'view' | 'create' | 'update' | 'delete' | 'stats' | 'manage'): boolean => {
    const { hasPermission } = useAuth();
    return hasPermission(`knowledge:base:${action}`);
  },

  /**
   * 获取用户可用的知识库操作
   */
  getAvailableKbActions: (): string[] => {
    const actions = ['list', 'view', 'create', 'update', 'delete', 'stats', 'manage'];
    return actions.filter(action => authUtils.checkKbPermission(action as any));
  },

  /**
   * 检查是否可以访问知识库页面
   */
  canAccessKbPage: (): boolean => {
    const { hasPermission } = useAuth();
    return hasPermission([
      'knowledge:base:manage',
      'knowledge:base:list',
      'knowledge:base:view'
    ]);
  }
};
