<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <el-tabs class="demo-tabs" ref="productAlert" v-model="activeName" tab-position="top"
                style="min-height:400px;" @tab-click="tabChange">
                <el-tab-pane name="basic">
                    <template #label>
                        <span class="custom-tabs-label">基本信息</span>
                    </template>
                    <el-form ref="DialogFormRef" :model="form" :rules="rules" label-width="100px">
                        <el-row :gutter="100">
                            <el-col :span="12">
                                <el-form-item label="设备名称" prop="deviceName">
                                    <el-input v-model="form.deviceName" placeholder="请输入设备名称">
                                    </el-input>
                                </el-form-item>
                                <el-form-item label="" prop="productName">
                                    <template #label>
                                        <span class="custom-tabs-label">
                                            <span><span style="color:red;"> * </span>所属产品</span>
                                        </span>
                                    </template>
                                    <el-input readonly v-model="form.productName" placeholder="请选择产品" :disabled="true">
                                      <template #append>
                                        <div
                                            style="cursor: pointer; color: #409EFF;"
                                            :style="{ color: form.status == 3 ? '#ccc' : '#409EFF' }"
                                            @click="form.status == 1 ? selectProduct() : null" >选择</div>
                                      </template>
                                    </el-input>
                                </el-form-item>
                                <el-form-item label="" prop="serialNumber">
                                    <template #label>
                                        <span class="custom-tabs-label">
                                            <span><span style="color:red;"> * </span>设备编号</span>
                                        </span>
                                    </template>
                                    <el-input v-model="form.serialNumber" placeholder="请输入设备编号"
                                        :disabled="form.status == 3" maxlength="32">
                                      <template #append v-if="form.deviceType !== 3">
                                        <div
                                            style="cursor: pointer; color: #409EFF;"
                                            :style="{ color: form.status == 3 ? '#ccc' : '#409EFF' }"
                                            @click="form.status != 3 ? generateNum() : null">生成</div>
                                      </template>
                                      <template #append v-if="form.deviceType === 3">
                                        <div
                                            style="cursor: pointer; color: #409EFF;"
                                            :style="{ color: form.status == 3 ? '#ccc' : '#409EFF' }"
                                            @click="form.status != 3 ? genSipID() : null">生成</div>
                                      </template>
                                    </el-input>
                                </el-form-item>
                                <el-form-item label="备注信息" prop="remark">
                                    <el-input class="custom-textarea" maxlength="150" v-model="form.remark"
                                        type="textarea" placeholder="请输入内容" :rows="1" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="设备图片">
                                    <div style="margin-top: 10px;">
                                        <el-image v-if="form.imgUrl" style="width:100%;height:200px;border-radius:10px;"
                                            lazy :preview-src-list="[baseUrl + form.imgUrl]"
                                            :src="baseUrl + form.imgUrl" fit="cover"></el-image>
                                        <el-image v-else-if="form.deviceType === 2"
                                            style="width:100%;height:200px;border-radius:10px;"
                                            :preview-src-list="[gateway]" :src="gateway" fit="cover"></el-image>
                                        <el-image v-else-if="form.deviceType === 3"
                                            style="width:100%;height:200px;border-radius:10px;"
                                            :preview-src-list="[video]" :src="video" fit="cover"></el-image>
                                        <el-image v-else style="width: 100%; height: 200px; border-radius: 10px;"
                                            :preview-src-list="[productimg]" :src="productimg" fit="cover"></el-image>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24" style="text-align: center; margin: 40px 0;">
                            <el-form-item>
                              <el-button
                                  type="primary"
                                  @click="submitForm(DialogFormRef)"
                                  v-auths="['iot:product:edit']"
                                  v-if="form.deviceId != 0"
                              >
                                修改
                              </el-button>
                              <el-button type="warning" @click="goBack" plain>关闭</el-button>
                            </el-form-item>
                          </el-col>
                        </el-row>
                    </el-form>
                </el-tab-pane>
                <!-- 运行状态 -->
                  <el-tab-pane name="runningStatus" v-if="form.deviceType !== 3">
                      <template #label>
                          <span class="custom-tabs-label">运行状态</span>
                      </template>
                      <div v-if="form != undefined">
                          <running-status v-model="form" />
                      </div>
                    <el-row>
                      <el-col :span="24" style="text-align: center; margin: 40px 0;">
                        <el-form-item>
                          <el-button
                              type="primary"
                              @click="submitForm(DialogFormRef)"
                              v-auths="['iot:product:edit']"
                              v-if="form.deviceId != 0"
                          >
                            修改
                          </el-button>
                          <el-button type="warning" @click="goBack" plain>关闭</el-button>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-tab-pane>
                  <el-tab-pane name="deviceSub" :disabled="form.deviceId == 0"
                      v-if="isSubDev && form.deviceType !== 3">
                      <template #label>
                          <span class="custom-tabs-label">子设备</span>
                      </template>
                      <device-sub ref="deviceSub" :device="form" />
                  </el-tab-pane>
                  <el-tab-pane name="sipChannel" :disabled="form.deviceId == 0" v-if="form.deviceType === 3">
                      <template #label>
                          <span class="custom-tabs-label">设备通道</span>
                      </template>
                  </el-tab-pane>
                  <el-tab-pane :disabled="form.deviceId == 0" v-if="form.deviceType === 3" name="sipPlayer">
                      <template #label>
                          <span class="custom-tabs-label">设备直播</span>
                      </template>
                  </el-tab-pane>
                  <el-tab-pane :disabled="form.deviceId == 0" v-if="form.deviceType === 3" name="sipVideo">
                      <template #label>
                          <span class="custom-tabs-label">直播录像</span>
                      </template>
                  </el-tab-pane>

                  <el-tab-pane name="deviceTimer" :disabled="form.deviceId == 0"
                      v-if="form.deviceType !== 3 && hasShrarePerm('timer')">
                      <template #label>
                          <span class="custom-tabs-label">设备定时</span>
                      </template>
                      <devices-timer ref="deviceTimer" :device="form" />
                  </el-tab-pane>
                  <el-tab-pane name="deviceAlarm" :disabled="form.deviceId == 0"
                      v-if="form.deviceType !== 3 && hasShrarePerm('alarm')" lazy>
                      <template #label>
                          <span class="custom-tabs-label">设备告警</span>
                      </template>
                      <devices-alarm ref="deviceAlarm" :device="form" />
                  </el-tab-pane>
                  <el-tab-pane name="devicesTicket" :disabled="form.deviceId == 0"
                               v-if="form.deviceType !== 3 && hasShrarePerm('alarm')" lazy>
                    <template #label>
                      <span class="custom-tabs-label">设备工单</span>
                    </template>
                    <devices-ticket ref="deviceTicketRef" :device="form" />
                  </el-tab-pane>
                  <el-tab-pane name="deviceLog" :disabled="form.deviceId == 0"
                      v-if="form.deviceType !== 3 && hasShrarePerm('log')" lazy>
                      <template #label>
                          <span class="custom-tabs-label">事件日志</span>
                      </template>
                      <devices-log ref="deviceLog" :device="form" />
                  </el-tab-pane>
                  <el-tab-pane name="deviceFuncLog" :disabled="form.deviceId == 0"
                      v-if="form.deviceType !== 3 && hasShrarePerm('log')" lazy>
                      <template #label>
                          <span class="custom-tabs-label">指令日志</span>
                      </template>
                      <devices-func ref="deviceFuncLog" :device="form" />
                  </el-tab-pane>
                  <el-tab-pane name="deviceMonitor" :disabled="form.deviceId == 0"
                      v-if="form.deviceType !== 3 && hasShrarePerm('monitor')">
                      <template #label>
                          <span class="custom-tabs-label">实时监测</span>
                      </template>
                      <devices-monitor ref="deviceMonitor" :device="form" />
                  </el-tab-pane>
                  <el-tab-pane name="deviceStastic" :disabled="form.deviceId == 0"
                      v-if="form.deviceType !== 3 && hasShrarePerm('statistic')">
                      <template #label>
                          <span class="custom-tabs-label">监测统计</span>
                      </template>
                      <devices-statistic ref="deviceStatistic" :device="form" />
                  </el-tab-pane>
            </el-tabs>

            <!--添加从机对话框-->
            <el-dialog :title="state.tableData.dialog.title" v-model="state.tableData.dialog.isShowDialog"
                width="1000px" style="position: absolute; top: 100px;" append-to-body>
                <el-form ref="tempRef" :inline="true">
                    <el-form-item label="产品名称" size="default">
                        <el-input v-model="state.tableData.param.productName" placeholder="模板名称">
                        </el-input>
                    </el-form-item>
                    <el-form-item size="small">
                        <el-button type="primary" size="small"
                            @click="queryTemp"><el-icon><ele-Search /></el-icon>搜索</el-button>
                        <el-button size="small" @click="resetQuery"><el-icon><ele-Refresh /></el-icon>重置</el-button>
                    </el-form-item>
                </el-form>
                <el-table v-loading="state.tableData.loading" :data="state.tableData.data" highlight-current-row
                    ref="multipleTable" style="width: 100%" border size="small" @row-click="rowClick"
                    :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                    <el-table-column label="选择" width="50" align="center">
                        <template #default="scope">
                            <input type="radio" :checked="scope.row.isSelect" name="product" />
                        </template>
                    </el-table-column>
                    <el-table-column label="产品名称" align="center" prop="productName" />
                    <el-table-column label="分类名称" align="center" prop="categoryName" />
            
                    <el-table-column label="授权码" align="center" prop="status" width="70">
                        <template #default="scope">
                            <el-tag type="success" v-if="scope.row.isAuthorize == 1">启用</el-tag>
                            <el-tag type="info" v-if="scope.row.isAuthorize == 0">未启用</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="产品状态" align="center" prop="status">
                    <template #default="scope">
                        <dict-tag :options="product_status_list" :value="scope.row.status" />
                    </template>
                </el-table-column>
                    <el-table-column label="设备类型" align="center" prop="deviceType">
                        <template #default="scope">
                            <dict-tag :options="device_type_list" :value="scope.row.deviceType" />
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" align="center" prop="createTime" width="100">
                        <template #default="scope">
                            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination v-show="state.tableData.total > 0" :total="state.tableData.total" class="mt15"
                    style="justify-content: flex-end;" size="small" layout="total, prev, pager, next"
                    v-model:current-page="state.tableData.param.pageNum"
                    v-model:page-size="state.tableData.param.pageSize" @size-change="ontempHandleSizeChange" background
                    @current-change="ontempHandleCurrentChange" />
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="cancel">取 消</el-button>
                        <el-button type="primary" @click="submitSelect">确 定</el-button>
                    </span>
                </template>
            </el-dialog>
        </el-card>
        <!-- 认证信息 -->
        <el-dialog title="Mqtt连接参数" v-model="openViewMqtt" width="600px" append-to-body>
            <el-form :model="listQuery" :rules="rules" label-width="150px">
                <el-form-item label="clientId" prop="clientId">
                    <el-input v-model="listQuery.clientId" disabled style="width: 80%" />
                </el-form-item>
                <el-form-item label="username" prop="username">
                    <el-input v-model="listQuery.username" disabled style="width: 80%" />
                </el-form-item>
                <el-form-item label="passwd" prop="passwd">
                    <el-input clearable v-model="listQuery.passwd" disabled style="width: 80%"></el-input>
                </el-form-item>
                <el-form-item label="port" prop="port">
                    <el-input clearable v-model="listQuery.port" disabled style="width: 80%"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="openViewMqtt = false">关 闭</el-button>
                    <el-button type="primary" @click="doCopy(2)">一键复制</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 二维码 -->
        <el-dialog v-model="openSummary" width="400px" append-to-body>
            <div
                style="  border: 1px solid #ccc;display: flex;flex-direction: column;justify-content: center;align-items: center; padding: 20px;">
                <QrcodeVue :value="qrText" :size="200"></QrcodeVue>
                <div style="padding-top: 30px;">设备二维码</div>
            </div>
        </el-dialog>
  </div>
</template>
<script setup lang="ts" name="">
import QrcodeVue from 'qrcode.vue';
import { nextTick, onMounted, onUnmounted, reactive, ref } from 'vue';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { getDeviceTemp } from '/@/api/iot/temp';
import { useRoute } from 'vue-router';
import { listProduct } from '/@/api/iot/product';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import { addDevice, generatorDeviceNum, getDevice, getDeviceRunningStatus, getMqttConnect, updateDevice } from '/@/api/iot/device';
import { getDeviceUser } from '/@/api/iot/deviceuser';
import { useUserInfo } from '/@/stores/userInfo';
import { cacheJsonThingsModel } from '/@/api/iot/template';
import { parseTime } from '/@/utils/next'
import { clientOut } from '/@/api/iot/netty';
import { loadBMap } from '/@/utils/map.js';
import router from '/@/router';
import mqttTool from '/@/utils/mqttTool';
import runningStatus from '/@/views/iot/device/running-status.vue';
import devicesTimer from '/@/views/iot/device/device-timer.vue';
import devicesLog from '/@/views/iot/device/device-log.vue';
import devicesFunc from '/@/views/iot/device/device-functionlog.vue';
import devicesMonitor from '/@/views/iot/device/device-monitor.vue';
import devicesStatistic from '/@/views/iot/device/device-statistic.vue';
import devicesAlarm from '/@/views/iot/device/devices-alarm.vue';
import devicesTicket from '/@/views/iot/device/device-ticket.vue';
const dictStore = useDictStore();  // 使用 Pinia store
const route = useRoute();
const userInfoStore = useUserInfo();
import gateway from '/@/assets/images/gateway.png';
import video from '/@/assets/images/video.png';
import productimg from '/@/assets/images/product.png';
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API)
declare global {
    const BMap: any;  // 声明 BMap 为 `any` 类型
}

interface productOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
interface RowData {
    productId: any;
    isSelect: any;
    productName: any;
    deviceType: any;
    tenantId: any;
    tenantName: any;

    // 其他字段...
}
// 定义变量内容
const DialogFormRef = ref();
// 使用 ref 引用子组件实例
const productAlert = ref(null);
let activeName = ref('basic')
let form = ref({
    deviceId: '' as any,
    deviceName: '',
    deviceType: 1 as any,
    firmwareVersion: 1.0 as any,
    isShadow: '' as any,
    isSimulate: 0 as any,
    latitude: '' as any,
    locationWay: 1 as any,
    longitude: '' as any,
    networkAddress: '' as any,
    productId: 0 as any,
    productName: '' as any,
    remark: '' as any,
    serialNumber: '' as any,
    status: 1 as any,
    tenantId: '' as any,
    tenantName: '' as any,
    imgUrl: '' as any
    // rssi: '' as any,   
    // networkIp: '' as any,
    // activeTime: '' as any,
    // summary: '' as any,
    // subDeviceList: null as any,
    // isOwner: '' as any,
    // userPerms: '' as any, 
})

const state = reactive({
    tableData: {
        data: [] as RowData[],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            status: 2,
            productName: ''
        },
        dialog: {
            isShowDialog: false,
            title: '',
        },
    },
});

const loading = ref(false)
const device_type_list = ref<productOption[]>([]); //设备类型
const transport_type_list = ref<productOption[]>([]);//传输协议
const network_method_list = ref<productOption[]>([]);//联网方式
const vertificate_method_list = ref<productOption[]>([]);//认证方式
const location_way_list = ref<productOption[]>([]);//定位方式
const collect_type_list = ref<productOption[]>([]);//采集方式
const device_status_list = ref<productOption[]>([]);//采集方式  
const product_status_list = ref<productOption[]>([]);
const channelId = ref('')
const deviceVideo = ref()
const deviceLiveStream = ref()
const deviceStatistic = ref()
const deviceTimer = ref()
const deviceSub = ref()
const deviceAlarm = ref()
const deviceTicketRef = ref() // 运维工单组件引用
const oldDeviceStatus = ref(null)  // 设备开始状态 
const isSubDev = ref(false) //用于判断是否是设备组(modbus)
const genDisabled = ref(false)// 生成设备编码是否禁用
//mqtt参数查看
let listQuery = ref({
    clientId: 0,
    username: '',
    passwd: '',
    port: '',
})
const openViewMqtt = ref(false)
// 二维码内容
const qrText = ref('fastbee')
// 打开设备配置对话框
const openSummary = ref(false)
const serverType = ref(1)
const openTip = ref(false)
const openServerTip = ref(false)
const deviceStatus = ref(0);
let product = reactive({
    productId: '',
    productName: '',
    deviceType: '',
    tenantId: '',
    tenantName: '',
    transport: '',
})
const map = ref()
const mk = ref(null)
// 校验规则
const rules = reactive({
    deviceName: [
        {
            required: true,
            message: '设备名称不能为空',
            trigger: 'blur',
        },
        {
            min: 2,
            max: 32,
            message: '设备名称长度在 2 到 32 个字符',
            trigger: 'blur',
        },
    ],
    firmwareVersion: [
        {
            required: true,
            message: '固件版本不能为空',
            trigger: 'blur',
        },
    ],

})
// 选项卡切换事件
const tabChange = (panel: any) => {
    if (form.value.deviceType == 3 && panel.name != 'deviceReturn') {
        if (panel.name === 'sipPlayer') {
            deviceVideo.value.destroy();
            if (channelId.value) {
                deviceLiveStream.value.channelId = channelId.value;
                deviceLiveStream.value.changeChannel();
            }
            if (deviceLiveStream.value.channelId) {
                deviceLiveStream.value.changeChannel();
            }
        } else if (panel.name === 'sipVideo') {
            deviceLiveStream.value.destroy();
            if (deviceVideo.value.channelId && deviceVideo.value.queryDate) {
                deviceVideo.value.loadDevRecord();
            }
        } else {
            deviceVideo.value.destroy();
            deviceLiveStream.value.destroy();
        }
    }
    nextTick(() => {
        // 获取标签页名称，兼容不同的参数格式
        const panelName = panel.name || panel.props?.name || panel.paneName;
        // 获取监测统计数据
        if (panelName === 'deviceStastic') {
            deviceStatistic.value.getListHistory();
        } else if (panelName === 'deviceTimer') {
            deviceTimer.value.getList();
        } else if (panelName === 'deviceSub') {
            if (form.value.serialNumber) {
                deviceSub.value.queryParams.gwDevCode = form.value.serialNumber;
                deviceSub.value.getList();
            }
        } else if (panelName === 'devicesTicket') {
            // 设备工单标签页 - 重新查询工单数据
            if (deviceTicketRef.value && form.value.serialNumber) {
                deviceTicketRef.value.getTableData();
            } else {
                console.warn('设备工单组件引用或设备序列号不存在，跳过数据刷新');
            }
        }
    });
};
// 二维码弹框
// const openSummaryDialog = () => {
//
//     let json = {
//         type: 1, // 1=扫码关联设备
//         deviceNumber: form.value.serialNumber,
//         productId: form.value.productId,
//         productName: form.value.productName,
//     };
//     qrText.value = JSON.stringify(json);
//     openSummary.value = true;
// }
// // 二维码弹框
// const openCodeDialog = () => {
//     let json = {
//         type: 1, // 1=扫码关联设备
//         deviceNumber: form.value.serialNumber,
//         productId: form.value.productId,
//         productName: form.value.productName,
//     };
//     qrText.value = JSON.stringify(json);
//     openSummary.value = true;
// }
// 复制
const doCopy = (type: any) => {
    if (type == 2) {
        const input = document.createElement('input');
        input.value = '{clientId:' + listQuery.value.clientId + ',username:' + listQuery.value.username + ',passwd:' + listQuery.value.passwd + ',port:' + listQuery.value.port + '}';
        document.body.appendChild(input);
        input.select(); //选中输入框
        document.execCommand('Copy'); //复制当前选中文本到前切板
        document.body.removeChild(input);
        ElMessage.success('复制成功');
    }
}
// 生成随机字母和数字
const generateNum = () => {
    if (!form.value.productId || form.value.productId == 0) {
        ElMessageBox.alert('请先选择产品');
        return;
    }
    genDisabled.value = true;
    const params = { type: serverType.value };
    generatorDeviceNum(params).then((response) => {
        form.value.serialNumber = response.data.data;
        genDisabled.value = false;
    });
}
/**获取设备详情*/
const getDeviceData = (deviceId: string) => {
    getDevice(deviceId).then(async (response) => {
        console.log(response, 'getDeviceData');
        if(response.data.code != 200) {
            ElMessage.error(response.data.msg);
            return;
        }
        // 分享设备获取用户权限
        response.data.userPerms = [];
        if (response.data.isOwner && response.data.isOwner == 0) {
            getDeviceUser(deviceId, userInfoStore.userInfos.userId,).then((permResponse) => {
                response.data.userPerms = permResponse.data.perms.split(',');
                // 获取设备状态和物模型
                getDeviceStatusWitchThingsModel(response.data);
            });
        } else {
            // 获取设备状态和物模型
            getDeviceStatusWitchThingsModel(response.data);
        }
    });
}
/**用户是否拥有分享设备权限*/
const hasShrarePerm = (permission: any) => {
    if (form.value.isOwner == 0) {
        // 分享设备权限
        if (form.value.userPerms.indexOf(permission) == -1) {
            return false;
        }
    }
    return true;
}
/** 获取缓存物模型*/
const getCacheThingsModdel = (productId: number) => {
    return new Promise((resolve, reject) => {
        cacheJsonThingsModel(productId)
            .then((response) => {
                resolve(JSON.parse(response.data.data));
            })
            .catch((error) => {
                reject(error);
            });
    });
}
/**获取设备运行状态*/
const getDeviceStatus = (data: { deviceId: any; slaveId: any; }) => {
    const params = {
        deviceId: data.deviceId,
        slaveId: data.slaveId,
    };
    return new Promise((resolve, reject) => {
        getDeviceRunningStatus(params)
            .then((response) => {
                resolve(response.data.data.thingsModels);
            })
            .catch((error) => {
                reject(error);
            });
    });
}
/** 获取设备状态和物模型 **/
const getDeviceStatusWitchThingsModel = async (response: any) => {
    // 获取缓存物模型
    response.data.cacheThingsModel = await getCacheThingsModdel(response.data.productId);
    // 获取设备运行状态
    response.data.thingsModels = await getDeviceStatus(response.data);

    //分享设备过滤没有权限的物模型
    if (response.data.isOwner == 0) {
        for (let i = 0; i < response.data.thingsModels.length; i++) {
            if (response.data.userPerms.indexOf(response.data.thingsModels[i].id) == -1) {
                response.data.thingsModels.splice(i--, 1);
            }
        }
    }
    // console.log(response.data,'formatThingsModel');

    // 格式化物模型，拆分出监测值,数组添加前缀
    formatThingsModel(response.data);
    form.value = response.data;
    // console.log(form.value, 'form.value');

    // 解析设备摘要
    // if (form.value.summary != null && form.value.summary != '') {
    //     summary.value = JSON.parse(form.value.summary);
    // }
    // isSubDev.value = form.value.subDeviceList && form.value.subDeviceList.length > 0;
    oldDeviceStatus.value = form.value.status;
    // loadMap();
    //Mqtt订阅
    connectMqtt();
    mqttSubscribe(form.value);
}
/**加载地图*/
// const loadMap = () => {
//     nextTick(() => {
//         loadBMap().then(() => {
//             getmap();
//         });
//     });
// }
// 地图定位
// const getmap = () => {
//     map.value = new BMap.Map('map');
//     let point = null;
//     if (form.value.longitude != null && form.value.longitude != '' && form.value.latitude != null && form.value.latitude != '') {
//         point = new BMap.Point(form.value.longitude, form.value.latitude);
//     } else {
//         point = new BMap.Point(116.404, 39.915);
//     }
//     map.value.centerAndZoom(point, 19);
//     map.value.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
//     map.value.addControl(new BMap.NavigationControl());
//
//     // 标注设备位置
//     mk.value = new BMap.Marker(point);
//     map.value.addOverlay(mk.value);
//     map.value.panTo(point);
// }
//mqtt参数查看
const handleViewMqtt = () => {
    openViewMqtt.value = true;
    const params = {
        deviceId: form.value.deviceId,
    };
    getMqttConnect(params).then((response) => {
        if (response.data.code == 200) {
            listQuery.value = response.data.data;
            // this.loading = false;
        }
    });
}
/** Mqtt订阅主题 */
const mqttSubscribe = (device: { productId: string; serialNumber: string; }) => {

    // 订阅当前设备状态和实时监测
    let topicStatus = '/' + device.productId + '/' + device.serialNumber + '/status/post';
    let topicProperty = '/' + device.productId + '/' + device.serialNumber + '/property/post';
    let topicFunction = '/' + device.productId + '/' + device.serialNumber + '/function/post';
    let topicMonitor = '/' + device.productId + '/' + device.serialNumber + '/monitor/post';
    let topicReply = '/' + device.productId + '/' + device.serialNumber + '/service/reply';
    let topics = [];
    let serviceTop = '/' + device.productId + '/' + device.serialNumber + '/ws/service';
    topics.push(serviceTop);

    topics.push(topicStatus);
    topics.push(topicFunction);
    topics.push(topicMonitor);
    topics.push(topicReply);
    /*modbus设备不订阅此topic*/
    // if (!isSubDev.value) {
    //     // topics.push(topicProperty);
    // }
    mqttTool.subscribe(topics);
}
/** Mqtt取消订阅主题 */
const mqttUnSubscribe = (device: { productId: string; serialNumber: string; }) => {
    console.log('+---++++++++');
    
    // 订阅当前设备状态和实时监测
    let topicStatus = '/' + device.productId + '/' + device.serialNumber + '/status/post';
    let topicProperty = '/' + device.productId + '/' + device.serialNumber + '/property/post';
    let topicFunction = '/' + device.productId + '/' + device.serialNumber + '/function/post';
    let topicMonitor = '/' + device.productId + '/' + device.serialNumber + '/monitor/post';
    let topicReply = '/' + device.productId + '/' + device.serialNumber + '/service/reply';
    let topics = [] as any;
    let serviceTop = '/' + device.productId + '/' + device.serialNumber + '/ws/service';
    topics.push(serviceTop);

    topics.push(topicStatus);
    topics.push(topicFunction);
    topics.push(topicMonitor);
    topics.push(topicReply);
    /*modbus设备不订阅此topic*/
    if (!isSubDev.value) {
        /*通过网关再转发*/
        // topics.push(topicProperty);
    }
    mqttTool.unsubscribe(topics);
}

/*选择模板*/
const selectProduct = () => {
    // this.reset();
    state.tableData.dialog.isShowDialog = true;
    state.tableData.dialog.title = "选择模板";
    getProductList();
}
/** 查询设备采集变量模板列表(弹框列表) */
const getProductList = async () => {
    try {
        state.tableData.loading = true;
        const response = await listProduct(state.tableData.param)
        //产品列表初始化isSelect值，用于单选
        for (let i = 0; i < response.data.rows.length; i++) {
            response.data.rows[i].isSelect = false;
        }
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
        if (form.value.productId != 0) {
            setRadioSelected(form.value.productId);
        }
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
            loading.value = false
        }, 500);
    }

}
/** 单选数据 */
const rowClick = (productdata: any) => {
    if (product != null) {
        setRadioSelected(productdata.productId);
        product = productdata as any;
    }
}
/** 设置单选按钮选中 */
const setRadioSelected = (productId: any) => {
    for (let i = 0; i < state.tableData.data.length; i++) {
        if (state.tableData.data[i].productId == productId) {
            state.tableData.data[i].isSelect = true;
        } else {
            state.tableData.data[i].isSelect = false;
        }
    }
}
/*按照模板名查询*/
const queryTemp = () => {
    getProductList();
}

/** 搜索按钮操作 */
const handleQuery = () => {
    state.tableData.param.pageNum = 1
    getProductList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param.pageNum = 1
    state.tableData.param.pageSize = 10
    state.tableData.param.status = 2
    state.tableData.param.productName = ''
    handleQuery()
}

// 取消按钮
const cancel = () => {
    state.tableData.dialog.isShowDialog = false
}
/*确认选择模板*/
const submitSelect = () => {
    console.log(product);
    state.tableData.dialog.isShowDialog = false
    form.value.productId = product.productId;
    form.value.productName = product.productName;
    form.value.deviceType = product.deviceType;
    getDeviceTempD();
    form.value.tenantId = product.tenantId;
    form.value.tenantName = product.tenantName;
    console.log(form.value, 'form.value', form.value.productName);

    if (product.transport === 'TCP') {
        openServerTip.value = true;
        serverType.value = 3;
    } else {
        openServerTip.value = false;
        serverType.value = 1;
    }

}
const getDeviceTempD = () => {
    getDeviceTemp(form.value).then((response) => {
        if (response.data.data && form.value.deviceType == 2) {
            openTip.value = true;
        } else {
            openTip.value = false;
        }
    });
}
// 获取状态数据
const getdictdata = async () => {
    try {
        device_type_list.value = await dictStore.fetchDict('iot_device_type')
        transport_type_list.value = await dictStore.fetchDict('iot_transport_type')
        network_method_list.value = await dictStore.fetchDict('iot_network_method')
        vertificate_method_list.value = await dictStore.fetchDict('iot_vertificate_method')
        location_way_list.value = await dictStore.fetchDict('iot_location_way')
        collect_type_list.value = await dictStore.fetchDict('data_collect_type')
        device_status_list.value = await dictStore.fetchDict('iot_device_status')
        product_status_list.value = await dictStore.fetchDict('iot_product_status')

        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};

const formatThingsModel = (data: { chartList: any[]; monitorList: any[]; staticList: any[]; thingsModels: any[]; }) => {
    data.chartList = [];
    data.monitorList = [];
    data.staticList = [];
    if (!data.thingsModels) {
        return
    }
    // 物模型格式化
    for (let i = 0; i < data.thingsModels.length; i++) {
        // 数字类型设置默认值并转换未数值
        if (data.thingsModels[i].datatype.type == 'integer' || data.thingsModels[i].datatype.type == 'decimal') {
            if (data.thingsModels[i].shadow == '') {
                data.thingsModels[i].shadow = Number(data.thingsModels[i].datatype.min);
            } else {
                data.thingsModels[i].shadow = Number(data.thingsModels[i].shadow);
            }
        }

        // 物模型分类放置
        if (data.thingsModels[i].datatype.type == 'array') {
            if (data.thingsModels[i].datatype.arrayType == 'object') {
                for (let k = 0; k < data.thingsModels[i].datatype.arrayParams.length; k++) {
                    for (let j = 0; j < data.thingsModels[i].datatype.arrayParams[k].length; j++) {
                        // 数组元素中参数ID添加前缀，例如：array_00_
                        let index = k > 9 ? String(k) : '0' + k;
                        let prefix = 'array_' + index + '_';
                        data.thingsModels[i].datatype.arrayParams[k][j].id = prefix + data.thingsModels[i].datatype.arrayParams[k][j].id;
                        // 图表、实时监测、监测统计分类放置
                        if (data.thingsModels[i].datatype.arrayParams[k][j].isChart == 1) {
                            // 图表
                            data.thingsModels[i].datatype.arrayParams[k][j].name = '[' + data.thingsModels[i].name + (k + 1) + '] ' + data.thingsModels[i].datatype.arrayParams[k][j].name;
                            data.thingsModels[i].datatype.arrayParams[k][j].datatype.arrayType = 'object';
                            data.chartList.push(data.thingsModels[i].datatype.arrayParams[k][j]);
                            if (data.thingsModels[i].datatype.arrayParams[k][j].isHistory == 1) {
                                // 监测统计
                                data.staticList.push(data.thingsModels[i].datatype.arrayParams[k][j]);
                            }
                            if (data.thingsModels[i].datatype.arrayParams[k][j].isMonitor == 1) {
                                // 实时监测
                                data.monitorList.push(data.thingsModels[i].datatype.arrayParams[k][j]);
                            }
                            data.thingsModels[i].datatype.arrayParams[k].splice(j--, 1);
                        }
                    }
                }
            } else {
                // 字符串拆分为物模型数组 model=id/name/type/isReadonly/value/shadow
                let values = data.thingsModels[i].value != '' ? data.thingsModels[i].value.split(',') : [];

                // let shadows = data.thingsModels[i].shadow != '' ? data.thingsModels[i].shadow.split(',') : [];
                for (let j = 0; j < data.thingsModels[i].datatype.arrayCount; j++) {
                    if (!data.thingsModels[i].datatype.arrayModel) {
                        data.thingsModels[i].datatype.arrayModel = [];
                    }
                    // 数组里面的ID需要添加前缀和索引，例如：array_00_temperature
                    let index = j > 9 ? String(j) : '0' + j;
                    let prefix = 'array_' + index + '_';
                    data.thingsModels[i].datatype.arrayModel[j] = {
                        id: prefix + data.thingsModels[i].id,
                        name: data.thingsModels[i].name,
                        type: data.thingsModels[i].type,
                        isReadonly: data.thingsModels[i].isReadonly,
                        value: values[j] ? values[j] : '',
                        deviceName:values[j] ? values[j] : ''
                        // shadow: shadows[j] ? shadows[j] : '',
                    };
                    
                    // if (data.thingsModels[i].datatype.arrayModel[j].value) {
                    //     listDeviceByGroup({ deviceId: values[j] }).then(res => {
                    //         // console.log(res.data,'res');                            
                    //         data.thingsModels[i].datatype.arrayModel[j].deviceName = res.data.rows[0].deviceName
                    //         console.log( data.thingsModels[i].datatype.arrayModel,'--+-+-+ data.thingsModels[i].datatype.arrayModel');
                    //     })
                        
                    // }
                }
            }
        } else if (data.thingsModels[i].datatype.type == 'object') {
            for (let j = 0; j < data.thingsModels[i].datatype.params.length; j++) {
                // 图表、实时监测、监测统计分类放置
                if (data.thingsModels[i].datatype.params[j].isChart == 1) {
                    // 图表
                    data.thingsModels[i].datatype.params[j].name = '[' + data.thingsModels[i].name + '] ' + data.thingsModels[i].datatype.params[j].name;
                    data.chartList.push(data.thingsModels[i].datatype.params[j]);
                    if (data.thingsModels[i].datatype.params[j].isHistory == 1) {
                        // 监测统计
                        data.staticList.push(data.thingsModels[i].datatype.params[j]);
                    }
                    if (data.thingsModels[i].datatype.params[j].isMonitor == 1) {
                        // 实时监测
                        data.monitorList.push(data.thingsModels[i].datatype.params[j]);
                    }
                    data.thingsModels[i].datatype.params.splice(j--, 1);
                }
            }
        } else if (data.thingsModels[i].isChart == 1) {
            // // 图表、实时监测、监测统计分类放置
            data.chartList.push(data.thingsModels[i]);
            if (data.thingsModels[i].isHistory == 1) {
                // 监测统计
                data.staticList.push(data.thingsModels[i]);
            }
            if (data.thingsModels[i].isMonitor == 1) {
                // 实时监测
                data.monitorList.push(data.thingsModels[i]);
            }
            // 使用i--解决索引变更问题
            data.thingsModels.splice(i--, 1);
        }
    }
    
}
/** 提交按钮 */
const submitForm = async (formEl: FormInstance | undefined) => {
    if (form.value.serialNumber == null || form.value.serialNumber == 0) {
        ElMessage.error('设备编号不能为空');
        return;
    }
    let reg = /^[0-9a-zA-Z]+$/;
    if (!reg.test(form.value.serialNumber)) {
        ElMessage.error('设备编号只能是字母和数字');
        return;
    }
    if (form.value.productId == null || form.value.productId == 0) {
        ElMessage.error('所属产品不能为空');
        return;
    }
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (form.value.deviceId != 0) {
                updateDevice(form.value).then(response => {                    
                    if( response.data.code != 200) {
                        ElMessage.error(response.data.msg);
                        return;
                    }
                    if (response.data.data == 0) {
                        ElMessage.error(response.data.msg);
                    } else {
                        ElMessage.success('修改成功');
                        form.value = JSON.parse(JSON.stringify(form.value));
                        getDeviceData(form.value.deviceId)
                        // loadMap();
                        //是否设备设置为禁用状态，则踢出设备
                        if (form.value.status === 2) {
                            const params = { clientId: form.value.serialNumber }
                            clientOut(params).then(res => { });
                        }
                    }
                    // ElMessageBox.confirm('修改成功', '系统提示', {
                    //     confirmButtonText: '确认',
                    //     cancelButtonText: '取消',
                    //     type: 'success',
                    // })
                }).catch(error => {
                    // getDeviceData(form.value.deviceId)
                });
            } else {
                addDevice(form.value).then(async (response) => {
                    // 获取设备状态
                    await getDeviceStatusWitchThingsModel(response.data);
                    if (form.value.deviceId == null || form.value.deviceId == 0) {
                        ElMessage.error('设备编号已经存在，添加设备失败');
                    } else {
                        if (form.value.status == 2) {
                            deviceStatus.value = 1;
                        }

                        ElMessageBox.confirm('添加设备成功', '系统提示', {
                            confirmButtonText: '确认',
                            cancelButtonText: '取消',
                            type: 'success',
                        })
                        // loadMap();
                    }
                });

            }

        } else {
            console.log('error submit!', fields)
        }
    })
}
/** 返回按钮 */
const goBack = () => {
    try {
        const obj = {
            path: "/iot/device",
            query: {
                t: Date.now(),
                pageNum: route.query.tabPanelName
            }
        };
        router.push(obj);
        reset();
    } catch (error) {
        console.error('Error during event handler:', error);
    }

}
// 表单重置
const reset = () => {
    form.value = {
        deviceId: '' as any,
        deviceName: '',
        deviceType: 1 as any,
        firmwareVersion: 1.0 as any,
        isShadow: '' as any,
        isSimulate: 0 as any,
        latitude: '' as any,
        locationWay: 1 as any,
        longitude: '' as any,
        networkAddress: '' as any,
        productId: 0 as any,
        productName: '' as any,
        remark: '' as any,
        serialNumber: '' as any,
        status: 1 as any,
        tenantId: '' as any,
        tenantName: '' as any,
    } as any
}
// 分页改变
const ontempHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getProductList();
};
// 分页改变
const ontempHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getProductList();
};
/* 连接Mqtt消息服务器 */
const connectMqtt = async () => {
    if (mqttTool.client == null) {
        await mqttTool.connect();
    }
    // mqttCallback();
}
/* Mqtt回调处理  */
const mqttCallback = () => {
    if (mqttTool.client) {
        mqttTool.client.on('message', (topic, message, buffer) => {
            let topics = topic.split('/');
            let productId = topics[1];
            let deviceNum = topics[2];
            const parsedMessage = JSON.parse(message.toString());

            if (!parsedMessage) {
                return;
            }
            if (topics[3] == 'status') {
                console.log('接收到【设备状态】主题：', topic);
                console.log('接收到【设备状态】内容：', parsedMessage);
                // 更新列表中设备的状态
                // 更新列表中设备的状态
                if (form.value.serialNumber == deviceNum) {
                    oldDeviceStatus.value = parsedMessage.status;
                    form.value.status = parsedMessage.status;
                    form.value.isShadow = parsedMessage.isShadow;
                    form.value.rssid = parsedMessage.rssid;
                }
            }
        });
    }
}
// 页面加载时
onMounted(() => {
    let activedName = route.query.activeName
    if (activedName != null && activedName != '') {
        activeName.value = activedName as any;
    }
    // 获取设备信息
    form.value.deviceId = route.query && route.query.deviceId;
    if (form.value.deviceId != 0) {
        connectMqtt();
        getDeviceData(form.value.deviceId);
    }
    isSubDev.value = route.query.isSubDev == 1 as any ? true : false;
    getdictdata()
});
onUnmounted(() => {
    mqttUnSubscribe(form.value);
});
</script>
<style scoped>
:deep(.el-button--small) {
    padding: 16px 16px;
    font-size: 14px;
}

:deep(.custom-textarea .el-textarea__inner) {
    min-height: 80px !important;
    /* 设置最小高度 */
    max-height: 150px;
    /* 设置最大高度 */
    overflow-y: auto;
    /* 如果超出最大高度，显示滚动条 */
}
</style>