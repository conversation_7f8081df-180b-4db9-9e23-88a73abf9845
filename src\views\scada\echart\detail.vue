<template>
    <div class="echart-detail-wrap">
        <el-row :gutter="12">
            <el-col :span="12">
                <el-card class="card-wrap">
                    <template #header>
                        <span>图表编辑&nbsp;-&nbsp;</span>
                        <a href="https://www.isqqw.com/" target="_blank" style="color: #1890ff">图表库</a>
                        <el-button style="float: right; padding: 3px 0" type="primary" link @click="loadEchartDatas"><el-icon><Refresh /></el-icon>运行</el-button>
                        <el-button style="float: right; padding: 3px 0; margin-right: 10px;" type="info" link @click="showCodeExample">
                            <el-icon><QuestionFilled /></el-icon>示例
                        </el-button>
                        <el-button style="float: right; padding: 3px 0; margin-right: 10px; color: #e6a23c" type="primary" link
                            @click="handleSubmitForm" v-auths="['scada:echart:edit']">
                            <svg-icon icon-class="save" />
                            保存
                        </el-button>
                    </template>
                    <monaco-editor v-model="code" language="javascript" @change="handleEditorChange" />
                </el-card>
            </el-col>
            <el-col :span="12">
                <el-card class="card-wrap">
                    <template #header>
                        <span>图表展示</span>
                        <el-button style="float: right; padding: 3px 0" type="primary" link @click="handleDownloadImage" ><el-icon><Download /></el-icon>下载</el-button>
                    </template>
                    <div ref="echartRef" style="height: 75vh; width: 100%"></div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus';
import { Refresh, Download, QuestionFilled } from '@element-plus/icons-vue';
import { getEchart, updateEchart } from '/@/api/scada/echart';
import html2canvas from 'html2canvas';
import * as echarts from 'echarts';
import { safeInitECharts } from '@/utils/echarts-config';
import MonacoEditor from '/@/components/MonacoEditor/index.vue';
const code = ref('')
// 定义组件名称
defineOptions({
    name: 'EchartDetail'
});

// 路由
const route = useRoute();

// 响应式数据
const form = ref<any>({
    echartData: '',
    echartName: '',
    base64: ''
});
const echart = ref<any>(null);
const refreshTimer = ref<any>(null);

// 引用
const echartRef = ref();

// 获取详情
const getDetail = () => {
    getEchart(route.query.id as string).then((res) => {
      res = res.data
        if (res.code === 200) {
            form.value = res.data;
            code.value = form.value.echartData || '';
            loadEchartDatas();
        }
    });
};

// 数据加载
const loadEchartDatas = () => {
    if (echart.value) {
        echart.value.dispose(); // 销毁ECharts实例
    }

    if (!form.value.echartData) {
        return;
    }

    try {
        // 检查代码是否包含基本的option定义
        if (!form.value.echartData.includes('option')) {
            console.warn('ECharts代码中未找到option变量定义');
            ElMessage.warning('图表代码中缺少option变量定义');
            return;
        }

        let funStr = getFun(form.value.echartData);
        let fun = eval('(' + funStr + ')');
        let option = fun(echarts);

        // 验证和修复ECharts配置
        if (option && typeof option === 'object') {
            // 确保series是数组且每个series都有type属性
            if (option.series) {
                if (!Array.isArray(option.series)) {
                    option.series = [option.series];
                }

                // 深度验证和修复series配置
                option.series = option.series.map((series: any, index: number) => {
                    if (!series || typeof series !== 'object') {
                      // eslint-disable-next-line no-console
                        console.warn(`Series at index ${index} is invalid, skipping`);
                        return null;
                    }

                    // 确保每个series都有type属性
                    if (!series.type) {
                        series.type = 'line'; // 默认类型
                      // eslint-disable-next-line no-console
                        console.warn(`Series at index ${index} missing type, defaulting to 'line'`);
                    }

                    // 确保数据存在
                    if (!series.data) {
                        series.data = [];
                    }

                    // 特殊处理极坐标柱状图
                    if (series.type === 'bar' && (series.coordinateSystem === 'polar' || option.polar)) {
                        // 确保极坐标柱状图有必要的配置
                        if (!series.coordinateSystem) {
                            series.coordinateSystem = 'polar';
                        }

                        // 确保有polarIndex
                        if (series.polarIndex === undefined) {
                            series.polarIndex = 0;
                        }

                        // 确保有radiusAxisIndex和angleAxisIndex
                        if (series.radiusAxisIndex === undefined) {
                            series.radiusAxisIndex = 0;
                        }
                        if (series.angleAxisIndex === undefined) {
                            series.angleAxisIndex = 0;
                        }

                        // 验证数据格式
                        if (Array.isArray(series.data)) {
                            series.data = series.data.map((item: any) => {
                                if (typeof item === 'number') {
                                    return item;
                                } else if (Array.isArray(item)) {
                                    return item;
                                } else if (item && typeof item === 'object' && item.value !== undefined) {
                                    return item;
                                } else {
                                    return 0; // 默认值
                                }
                            });
                        }
                    }

                    return series;
                }).filter(Boolean); // 移除无效的series

                // 如果没有有效的series，创建一个默认的
                if (option.series.length === 0) {
                    option.series = [{
                        type: 'line',
                        data: []
                    }];
                }
            } else {
                // 如果没有series，创建一个默认的
                option.series = [{
                    type: 'line',
                    data: []
                }];
            }

            // 确保tooltip显示数据值
            if (!option.tooltip) {
                option.tooltip = {};
            }

            // 根据图表类型设置不同的tooltip配置
            const hasRadarChart = option.series && option.series.some((s: any) => s.type === 'radar');
            const hasPieChart = option.series && option.series.some((s: any) => s.type === 'pie');

            if (hasRadarChart) {
                option.tooltip.trigger = 'item';
            } else if (hasPieChart) {
                option.tooltip.trigger = 'item';
                option.tooltip.formatter = '{a} <br/>{b}: {c} ({d}%)';
            } else {
                option.tooltip.trigger = 'axis';
                option.tooltip.axisPointer = {
                    type: 'cross',
                    label: {
                        backgroundColor: '#6a7985'
                    }
                };
            }

            // 确保x轴时间数据按升序显示（仅对直角坐标系）
            if (option.xAxis && !option.polar && !option.radar) {
                if (option.xAxis.data && Array.isArray(option.xAxis.data)) {
                    option.xAxis.data.sort((a: any, b: any) => {
                        // 尝试解析为时间进行排序
                        const timeA = new Date(a).getTime();
                        const timeB = new Date(b).getTime();
                        if (!isNaN(timeA) && !isNaN(timeB)) {
                            return timeA - timeB;
                        }
                        return 0;
                    });
                }

                // 如果是多个x轴的情况
                if (Array.isArray(option.xAxis)) {
                    option.xAxis.forEach((axis: any) => {
                        if (axis && axis.data && Array.isArray(axis.data)) {
                            axis.data.sort((a: any, b: any) => {
                                const timeA = new Date(a).getTime();
                                const timeB = new Date(b).getTime();
                                if (!isNaN(timeA) && !isNaN(timeB)) {
                                    return timeA - timeB;
                                }
                                return 0;
                            });
                        }
                    });
                }
            }

            // 验证和修复极坐标系配置
            const hasPolarSeries = option.series && option.series.some((s: any) =>
                s.coordinateSystem === 'polar' || s.type === 'bar' && option.polar
            );

            if (hasPolarSeries || option.polar) {
                // 确保polar配置存在且完整
                if (!option.polar) {
                    option.polar = [{}];
                } else if (!Array.isArray(option.polar)) {
                    option.polar = [option.polar];
                }

                // 验证每个polar配置
                option.polar = option.polar.map((polar: any) => {
                    if (!polar || typeof polar !== 'object') {
                        polar = {};
                    }

                    if (!polar.center) {
                        polar.center = ['50%', '50%'];
                    }
                    if (!polar.radius) {
                        polar.radius = '80%';
                    }

                    return polar;
                });

                // 确保有对应的角度轴和径向轴配置
                if (!option.angleAxis) {
                    option.angleAxis = [{ type: 'category', data: [] }];
                } else if (!Array.isArray(option.angleAxis)) {
                    option.angleAxis = [option.angleAxis];
                }

                if (!option.radiusAxis) {
                    option.radiusAxis = [{ type: 'value' }];
                } else if (!Array.isArray(option.radiusAxis)) {
                    option.radiusAxis = [option.radiusAxis];
                }

                // 验证极坐标系的series配置
                option.series.forEach((series: any) => {
                    if (series.coordinateSystem === 'polar' || (series.type === 'bar' && option.polar)) {
                        series.coordinateSystem = 'polar';

                        // 确保索引配置正确
                        if (series.polarIndex === undefined) {
                            series.polarIndex = 0;
                        }
                        if (series.radiusAxisIndex === undefined) {
                            series.radiusAxisIndex = 0;
                        }
                        if (series.angleAxisIndex === undefined) {
                            series.angleAxisIndex = 0;
                        }

                        // 确保索引不超出范围
                        series.polarIndex = Math.min(series.polarIndex, option.polar.length - 1);
                        series.radiusAxisIndex = Math.min(series.radiusAxisIndex, option.radiusAxis.length - 1);
                        series.angleAxisIndex = Math.min(series.angleAxisIndex, option.angleAxis.length - 1);
                    }
                });
            }

            // 最终验证：确保所有series都有有效的配置
            const finalValidation = () => {
                if (!option.series || !Array.isArray(option.series)) {
                    return false;
                }

                return option.series.every((series: any) => {
                    if (!series || typeof series !== 'object' || !series.type) {
                        return false;
                    }

                    // 对于极坐标柱状图的特殊验证
                    if (series.type === 'bar' && series.coordinateSystem === 'polar') {
                        return (
                            series.polarIndex !== undefined &&
                            series.radiusAxisIndex !== undefined &&
                            series.angleAxisIndex !== undefined &&
                            Array.isArray(series.data)
                        );
                    }

                    return true;
                });
            };

            let view = echartRef.value;
            if (view && finalValidation()) {
                echart.value = safeInitECharts(view, 'light') || echarts.init(view, 'light');

                // 在设置选项前确保完整的颜色配置
                if (option && typeof option === 'object') {
                    // 设置全局颜色调色板
                    if (!option.color) {
                        option.color = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'];
                    }

                    if (option.series && Array.isArray(option.series)) {
                        const defaultColors = option.color;
                        option.series = option.series.map((series: any, index: number) => {
                            if (!series || typeof series !== 'object') {
                                return {
                                    name: `系列${index + 1}`,
                                    type: 'bar',
                                    data: [],
                                    itemStyle: {
                                        color: defaultColors[index % defaultColors.length],
                                        borderColor: defaultColors[index % defaultColors.length],
                                        borderWidth: 0
                                    },
                                    lineStyle: {
                                        color: defaultColors[index % defaultColors.length]
                                    }
                                };
                            }

                            const color = series.itemStyle?.color || defaultColors[index % defaultColors.length];

                            const enhancedSeries = {
                                ...series,
                                itemStyle: {
                                    color: color,
                                    borderColor: color,
                                    borderWidth: 0,
                                    ...series.itemStyle
                                },
                                lineStyle: {
                                    color: color,
                                    ...series.lineStyle
                                }
                            };

                            // 为面积图添加 areaStyle
                            if (series.type === 'line' && series.areaStyle) {
                                enhancedSeries.areaStyle = {
                                    color: color,
                                    opacity: 0.3,
                                    ...series.areaStyle
                                };
                            }

                            return enhancedSeries;
                        });
                    }

                    // 确保 tooltip 配置安全
                    if (!option.tooltip) {
                        option.tooltip = {};
                    }
                    option.tooltip.confine = true;
                }

                // 使用try-catch包装setOption调用
                try {
                    echart.value.setOption(option, true); // 使用notMerge=true避免配置冲突
                } catch (setOptionError: any) {
                  // eslint-disable-next-line no-console
                    console.error('ECharts setOption error:', setOptionError);

                    // 创建安全的降级配置
                    const fallbackOption = {
                        title: { text: '配置错误' },
                        tooltip: { trigger: 'axis', show: true },
                        xAxis: { type: 'category', data: ['错误'] },
                        yAxis: { type: 'value' },
                        series: [{
                            name: '错误',
                            type: 'bar',
                            data: [0],
                            itemStyle: { color: '#ff6b6b' }
                        }]
                    };

                    try {
                        echart.value.setOption(fallbackOption);
                        ElMessage.warning('图表已使用默认配置');
                    } catch (fallbackError) {
                        // eslint-disable-next-line no-console
                        console.error('降级配置也失败:', fallbackError);
                        ElMessage.error('图表渲染完全失败');
                    }
                }
            } else if (view) {
              // eslint-disable-next-line no-console
                console.error('ECharts configuration validation failed');
                ElMessage.error('图表配置验证失败');
            }
        } else {
          // eslint-disable-next-line no-console
            console.error('Invalid ECharts option:', option);
        }
    } catch (error) {
      // eslint-disable-next-line no-console
        console.error('Error loading ECharts data:', error);

        // 提供更详细的错误信息
        let errorMessage = '图表配置错误，请检查代码格式';
        if (error.message.includes('Cannot access') && error.message.includes('before initialization')) {
            errorMessage = '变量初始化错误：请确保在使用option变量前先声明它（如：let option = {...}）';
        } else if (error.message.includes('has already been declared')) {
            errorMessage = '变量重复声明错误：option变量已被声明，请检查代码中是否有重复的声明';
        } else if (error.message.includes('Unexpected token')) {
            errorMessage = '代码语法错误：请检查JavaScript语法是否正确';
        } else if (error.message.includes('is not defined')) {
            errorMessage = '变量未定义错误：请检查所有变量是否正确声明';
        }

        ElMessage.error(errorMessage);
    }
};

const getFun = (optionStr: string) => {
    // 检查用户代码是否已经声明了option变量
    const hasOptionDeclaration = /(?:let|const|var)\s+option\s*=/.test(optionStr);

    return `
        (function(echarts) {
            try {
                ${hasOptionDeclaration ? '' : 'let option;'}
                ${optionStr}
                return (typeof option !== 'undefined') ? option : {};
            } catch (error) {
                console.error('ECharts option generation error:', error);
                return {};
            }
        })
    `;
};

// 编辑器变化处理
const handleEditorChange = (data: string) => {
    form.value.echartData = data;
};

// 提交按钮
const handleSubmitForm = () => {
    let canvasBox = echartRef.value;
    const loadingInstance = ElLoading.service({
        text: '保存中，请稍候...',
        background: 'rgba(0, 0, 0, 0.7)'
    });

    html2canvas(canvasBox).then(function (canvas) {
        form.value.base64 = canvas.toDataURL('image/png');
        updateEchart(form.value).then((res) => {
          res = res.data
            if (res.code === 200) {
                ElMessage.success('修改成功');
            }
            loadingInstance.close();
        });
    });
};

// 页面元素转图片
const handleDownloadImage = () => {
    // 转换成canvas
    let canvasBox = echartRef.value;
    html2canvas(canvasBox).then(function (canvas) {
        var img = canvas.toDataURL('image/png').replace('image/png', 'image/octet-stream');
        // 创建a标签，实现下载
        var creatIMg = document.createElement('a');
        creatIMg.download = `${form.value.echartName}.png`; // 设置下载的文件名，
        creatIMg.href = img; // 下载url
        document.body.appendChild(creatIMg);
        creatIMg.click();
        creatIMg.remove(); // 下载之后把创建的元素删除
    });
};

// 显示代码示例
const showCodeExample = () => {
    const exampleCode = `// 正确的ECharts代码示例
let option = {
    title: {
        text: '示例图表',
        left: 'center'
    },
    tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    xAxis: {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    },
    yAxis: {
        type: 'value'
    },
    series: [{
        name: '访问来源',
        type: 'line',
        data: [120, 200, 150, 80, 70, 110, 130]
    }]
};

// 注意：必须声明option变量并返回它`;

    ElMessageBox.alert(exampleCode, '代码示例', {
        confirmButtonText: '确定',
        customClass: 'code-example-dialog',
        dangerouslyUseHTMLString: false
    });
};

// 启动自动刷新
const startAutoRefresh = () => {
    // 每1分钟自动刷新图表数据
    refreshTimer.value = setInterval(() => {
        if (form.value.echartData) {
            loadEchartDatas();
        }
    }, 60000); // 60秒 = 1分钟
};

// 停止自动刷新
const stopAutoRefresh = () => {
    if (refreshTimer.value) {
        clearInterval(refreshTimer.value);
        refreshTimer.value = null;
    }
};

// 生命周期
onMounted(() => {
    getDetail();
    startAutoRefresh();
});

onBeforeUnmount(() => {
    stopAutoRefresh();
    if (echart.value) {
        echart.value.dispose();
    }
});
</script>
<style lang="scss" scoped>
.echart-detail-wrap {
    padding: 20px;

    .card-wrap {
        height: 86vh;
    }
}

:deep(.code-example-dialog) {
    .el-message-box__message {
        font-family: 'Courier New', monospace;
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 4px;
        white-space: pre-wrap;
        font-size: 13px;
        line-height: 1.5;
        color: #333;
        border: 1px solid #ddd;
    }

    .el-message-box {
        width: 600px;
        max-width: 90vw;
    }
}
</style>
