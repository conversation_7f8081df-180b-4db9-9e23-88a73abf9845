<template>
  <template v-for="val in chils">
    <el-sub-menu :index="val.path" :key="val.path" v-if="val.children && val.children.length > 0">
      <template #title>
        <SvgIcon :name="val.meta.icon" :type="'menu'" :color="''" />
        <span>{{ val.meta.title }}</span>
      </template>
      <sub-item :chil="val.children" />
    </el-sub-menu>
    <!-- 外链菜单项 -->
    <li
        class="el-menu-item external-link-item"
        :key="val.path"
        v-else-if="(val.meta?.link || val.meta?.isLink) && !(val.meta?.isIframe)"
        @click="handleMenuItemClick(val)"
    >
      <div class="external-link">
        <SvgIcon :name="val.meta.icon" :type="'menu'" :color="''" />
        <span>{{ val.meta.title }}</span>
      </div>
    </li>

    <!-- 普通菜单项 -->
    <el-menu-item
        :index="val.path"
        :key="val.path"
        v-else
    >
      <template #title>
        <SvgIcon :name="val.meta.icon" :type="'menu'" :color="''" />
        <span>{{ val.meta.title }}</span>
      </template>
    </el-menu-item>

  </template>
</template>

<script setup lang="ts" name="navMenuSubItem">
import { computed } from 'vue';
import { RouteRecordRaw } from 'vue-router';
import other from '/@/utils/other';
import router from "@/router";

// 定义父组件传过来的值
const props = defineProps({
	// 菜单列表
	chil: {
		type: Array<RouteRecordRaw>,
		default: () => [],
	},
});

const handleMenuItemClick = (menuItem: any) => {
  const link = menuItem.meta?.link || menuItem.meta?.isLink;
  const target = menuItem.meta?.target || '_blank'; // 默认在新标签页打开

  if (link && /^https?:\/\//i.test(link)) {
    // 在新标签页打开，安全且不干扰原页面
    window.open(link, target, 'noopener,noreferrer');
  } else {
    router.push(menuItem.path);
  }
};
// 获取父级菜单数据
const chils = computed(() => {
	return <RouteItems>props.chil;
});
// 打开外部链接
const onALinkClick = (val: RouteItem) => {
	other.handleOpenLink(val);
};
</script>
