import request from '@/utils/request'

// 驱动调试相关API接口

/**
 * 获取支持调试的插件列表
 */
export function getDebugPlugins() {
  return request({
    url: '/openApi/runtimeInfo/getAllPlugins',
    method: 'get'
  })
}

/**
 * 获取插件调试UI信息
 * @param pluginName 插件全名称
 */
export function getDebugUIInfo(pluginName: string) {
  return request({
    url: '/openApi/driverDebug/getDebugUIInfo',
    method: 'get',
    params: { pluginName }
  })
}

/**
 * 获取插件属性配置
 * @param pluginName 插件全名称
 */
export function getPluginProperties(pluginName: string) {
  return request({
    url: '/openApi/driverDebug/getPluginProperties',
    method: 'get',
    params: { pluginName }
  })
}

/**
 * 获取插件方法信息
 * @param pluginName 插件全名称
 */
export function getPluginMethods(pluginName: string) {
  return request({
    url: '/openApi/driverDebug/getPluginMethods',
    method: 'get',
    params: { pluginName }
  })
}

/**
 * 执行插件方法
 * @param data 方法执行请求
 */
export function executeMethod(data: {
  pluginName: string
  methodName: string
  parameters: Record<string, any>
}) {
  return request({
    url: '/openApi/driverDebug/executeMethod',
    method: 'post',
    data
  })
}

// 类型定义
export interface DebugPluginInfo {
  fullName: string
  name: string
  fileName: string
  typeName: string
  description: string
  version: string
  hasDebugUI: boolean
  debugUITypeName: string
}

export interface DebugUIInfo {
  pluginName: string
  hasDebugUI: boolean
  debugUITypeName: string
  hasAddressUI: boolean
  addressUITypeName: string
  errorMessage: string
}

export interface PluginPropertiesInfo {
  pluginName: string
  properties: Record<string, string>
  editorItems: EditorItemInfo[]
  propertyUITypeName: string
  errorMessage: string
}

export interface EditorItemInfo {
  field: string
  text: string
  type: string
  isRequired: boolean
  defaultValue: string
}

export interface MethodExecuteRequest {
  pluginName: string
  methodName: string
  parameters: Record<string, any>
}

export interface MethodExecuteResult {
  success: boolean
  result: any
  executeTime: string
  errorMessage: string
} 