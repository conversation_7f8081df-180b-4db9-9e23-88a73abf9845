# Git协作开发完整流程指南

## 概述

本文档基于实际的知识库功能开发项目，详细记录了从创建开发分支到合并MR的完整Git协作流程，包括遇到的问题和解决方案。

## 目录

1. [开发环境准备](#1-开发环境准备)
2. [创建和切换开发分支](#2-创建和切换开发分支)
3. [功能开发和提交](#3-功能开发和提交)
4. [合并最新主分支](#4-合并最新主分支)
5. [推送和创建MR](#5-推送和创建mr)
6. [处理冲突和Stash](#6-处理冲突和stash)
7. [Git分支同步原理与实践](#7-git分支同步原理与实践)
   - 7.1 [Git分支同步原理](#71-git分支同步原理)
   - 7.2 [同步策略选择](#72-同步策略选择)
   - 7.3 [推送时机详细指南](#73-推送时机详细指南)
   - 7.4 [实际操作指南](#74-实际操作指南)
8. [最佳实践](#8-最佳实践)
9. [常见问题和解决方案](#9-常见问题和解决方案)

---

## 1. 开发环境准备

### 1.1 检查当前状态
```bash
# 查看当前分支和状态
git status

# 查看所有分支
git branch -a

# 确保在main分支且是最新的
git checkout main
git pull origin main
```

### 1.2 环境配置检查
```bash
# 检查开发服务器是否正常
npm run dev

# 检查依赖是否完整
npm install
```

---

## 2. 创建和切换开发分支

### 2.1 创建功能分支
```bash
# 基于最新main分支创建开发分支
git checkout -b Dev-Wjj

# 验证分支创建成功
git branch
```

**命令解释：**
- `git checkout -b Dev-Wjj`：创建并切换到新分支
- 分支命名规范：`Dev-{开发者名称}` 或 `feature/{功能名称}`

### 2.2 推送新分支到远程
```bash
# 首次推送新分支
git push -u origin Dev-Wjj
```

**命令解释：**
- `-u`：设置上游分支，后续可直接使用 `git push`

---

## 3. 功能开发和提交

### 3.1 开发过程中的提交
```bash
# 查看修改的文件
git status

# 添加特定文件
git add src/views/ai/kb/kbm/index.vue
git add src/api/iot/knowledgeBase.ts

# 或添加所有修改
git add .

# 提交更改
git commit -m "feat(知识库): 添加知识库管理功能模块"
```

### 3.2 提交信息规范
```
feat(模块): 功能描述        # 新功能
fix(模块): 修复描述         # 修复bug
chore(模块): 配置或工具更改  # 配置文件、构建工具等
docs(模块): 文档更新        # 文档修改
style(模块): 代码格式调整    # 不影响功能的格式修改
refactor(模块): 代码重构    # 重构代码
test(模块): 测试相关        # 测试代码
```

### 3.3 配置文件管理
```bash
# 添加.gitignore规则（避免临时文件被跟踪）
echo "# Cursor editor files" >> .gitignore
echo ".cursor/" >> .gitignore
echo ".cursorindexingignore" >> .gitignore
echo "" >> .gitignore
echo "# History and backup files" >> .gitignore
echo ".history/" >> .gitignore
echo ".specstory/" >> .gitignore
echo "" >> .gitignore
echo "# Local backup and documentation files" >> .gitignore
echo "backup-*.md" >> .gitignore
echo "backup-*.txt" >> .gitignore
echo "backup-*.patch" >> .gitignore
echo "git-reset-guide.md" >> .gitignore

# 提交.gitignore更新
git add .gitignore
git commit -m "chore(配置): 更新gitignore规则"
```

---

## 4. 合并最新主分支

### 4.1 为什么需要合并主分支
- 获取其他开发者的最新更改
- 避免后续合并冲突
- 确保功能基于最新代码开发

### 4.2 合并操作
```bash
# 获取最新的远程信息
git fetch origin

# 合并最新的main分支
git merge origin/main
```

### 4.3 处理合并冲突（如果有）
```bash
# 查看冲突文件
git status

# 手动编辑冲突文件，解决冲突标记
# <<<<<<< HEAD
# 您的更改
# =======
# 主分支的更改
# >>>>>>> origin/main

# 标记冲突已解决
git add 冲突文件名

# 完成合并
git commit -m "merge: 合并最新main分支"
```

---

## 5. 推送和创建MR

### 5.1 推送到远程分支
```bash
# 推送所有提交到远程分支
git push origin Dev-Wjj
```

### 5.2 在GitLab创建Merge Request
1. 访问GitLab项目页面
2. 点击"创建合并请求"按钮
3. 填写MR信息：
   ```
   标题：feat(知识库): 添加知识库管理功能模块
   
   描述：
   ## 功能概述
   添加完整的知识库管理功能模块
   
   ## 主要功能
   - 知识库管理页面 (kbm)
   - 文件管理页面 (fm) 
   - 知识库搜索页面 (kbs)
   - AI聊天功能页面 (chat)
   
   ## 技术实现
   - 新增知识库API接口和配置
   - 新增认证相关组合函数
   - 更新路由配置和权限指令
   
   ## 测试状态
   - ✅ 开发服务器正常启动
   - ✅ 所有依赖包正常安装
   ```

### 5.3 合并MR
- 等待代码审查（如果有流程要求）
- 点击"创建合并提交"完成合并

---

## 6. 处理冲突和Stash

### 6.1 Stash的使用场景
当需要临时保存未提交的更改时：
```bash
# 保存当前工作区更改
git stash push -m "临时保存配置文件更改"

# 查看stash列表
git stash list

# 应用stash
git stash pop

# 查看stash内容
git stash show -p stash@{0}
```

### 6.2 处理Stash冲突
```bash
# 应用stash时出现冲突
git stash pop
# Auto-merging .env.development
# CONFLICT (content): Merge conflict in .env.development

# 手动解决冲突后
git add .env.development
git commit -m "resolve: 解决stash应用冲突"
```

### 6.3 配置文件冲突处理原则
- **保留本地开发配置**：如API地址、端口等
- **合并通用配置**：如新增的配置项
- **不提交个人配置**：避免影响其他开发者

---

## 7. Git分支同步原理与实践

### 7.1 Git分支同步原理

#### 7.1.1 分支类型和关系
```bash
# 查看所有分支类型
git branch -a

# 输出示例：
* Dev-Wjj                    # 当前本地分支
  main                       # 本地主分支
  remotes/origin/Dev-Wjj     # 远程跟踪分支
  remotes/origin/main        # 远程主分支跟踪
```

**分支关系图：**
```
本地仓库                远程仓库 (GitLab)
├── main               ←→  origin/main
├── Dev-Wjj            ←→  origin/Dev-Wjj
└── remotes/origin/*   ←   (远程分支的本地缓存)
```

#### 7.1.2 本地合并 vs 远程合并

**本地合并 (`git merge main`)：**
```
影响范围：仅本地
├── 更新：本地Dev-Wjj分支 ✅
├── 更新：远程origin/Dev-Wjj ❌
├── 其他人可见：❌
└── 用途：本地开发环境同步
```

**远程合并 (推送后)：**
```
影响范围：本地+远程
├── 更新：本地Dev-Wjj分支 ✅
├── 更新：远程origin/Dev-Wjj ✅
├── 其他人可见：✅
└── 用途：团队协作共享
```

#### 7.1.3 远程同步机制

**关键原理：**
- ✅ **只有push操作才更新远程分支**
- ❌ **本地merge/commit不影响远程**
- ✅ **其他人只能获取已推送的内容**

**示例场景：**
```bash
# 您的操作
git merge main          # 本地合并，远程不变
git log --oneline -2    # 看到最新提交

# 其他开发者操作
git pull origin Dev-Wjj # 获取的是远程分支状态
git log --oneline -2    # 看不到您的本地合并
```

### 7.2 同步策略选择

#### 7.2.1 何时需要推送合并结果

**需要推送的情况：**
- ✅ **团队协作开发** - 多人在同一分支工作
- ✅ **功能分支共享** - 其他人需要基于您的分支开发
- ✅ **代码备份需求** - 重要更改需要远程备份
- ✅ **持续集成** - CI/CD需要最新代码

**推送命令：**
```bash
# 推送合并结果
git push origin Dev-Wjj

# 强制推送（谨慎使用）
git push -f origin Dev-Wjj
```

#### 7.2.2 何时不需要推送合并结果

**不需要推送的情况：**
- ✅ **个人开发分支** - 仅用于个人开发
- ✅ **功能已完成** - MR已合并，分支使命完成
- ✅ **本地配置更改** - 包含不应共享的配置
- ✅ **临时同步** - 仅为获取最新代码

**保持本地的好处：**
```bash
# 本地保持最新，用于开发
git merge main

# 远程保持简洁的功能历史
# origin/Dev-Wjj 仍然是纯净的功能分支
```

#### 7.2.3 术语澄清和概念区分

**远程合并操作（在平台上）：**
- **GitLab**: Merge Request (MR)
- **GitHub**: Pull Request (PR)
- **作用**: 将功能分支合并到主分支
- **位置**: 在远程仓库服务器上执行

**本地合并操作（在本地）：**
- **命令**: `git merge main`
- **作用**: 将main分支最新代码合并到当前分支
- **位置**: 在本地仓库中执行
- **目的**: 获取最新代码，保持开发环境同步

**关键区别：**
```
远程合并 (MR/PR): 功能分支 → main分支 (在服务器)
本地合并: main分支 → 功能分支 (在本地)
```

#### 7.2.4 本地合并后的推送决策

**决策流程图：**
```
本地执行 git merge main
         ↓
    是否有未提交的配置文件？
    ├─ 有 → 不推送 (避免配置文件冲突)
    └─ 没有 → 继续判断
         ↓
    是否为团队协作分支？
    ├─ 是 → 推送 (让团队获得最新代码)
    └─ 否 → 继续判断
         ↓
    功能是否还在开发中？
    ├─ 是 → 考虑推送 (根据团队需要)
    └─ 否 → 不推送 (功能已完成)
```

**推送决策矩阵：**

| 场景 | 推送建议 | 原因说明 |
|------|----------|----------|
| 个人开发分支 + 有配置文件更改 | ❌ 不推送 | 避免配置冲突，保持远程简洁 |
| 个人开发分支 + 功能已完成 | ❌ 不推送 | MR已合并，分支使命完成 |
| 团队协作分支 + 多人开发 | ✅ 推送 | 让团队成员获得最新代码 |
| 功能分支 + 需要CI/CD | ✅ 推送 | 触发自动化流程 |
| 临时同步 + 仅为获取代码 | ❌ 不推送 | 本地同步即可满足需求 |

**实际操作示例：**
```bash
# 场景1: 个人开发分支，不推送
git checkout Dev-Wjj
git merge main
# 停止，不执行 git push

# 场景2: 团队协作分支，推送
git checkout feature-team-work
git merge main
git push origin feature-team-work

# 场景3: 有配置文件，先处理再决定
git status  # 检查是否有配置文件更改
git stash push -m "本地配置"  # 如果有，先保存
git push origin Dev-Wjj       # 然后推送
git stash pop                 # 恢复配置
```

### 7.3 推送时机详细指南

#### 7.3.1 什么时候必须推送

**强制推送场景：**
```bash
# 1. 团队协作开发
git checkout feature-shared
git merge main
git push origin feature-shared  # 必须推送，让团队获得最新代码

# 2. 触发CI/CD流程
git merge main
git push origin feature-branch  # 触发自动化测试和部署

# 3. 代码审查要求
git merge main
git push origin feature-branch  # 审查者需要看到最新代码
```

#### 7.3.2 什么时候不应该推送

**禁止推送场景：**
```bash
# 1. 包含本地配置文件
git status
# 显示: M .env.development, M .env.production
# 不要推送，避免覆盖其他人的配置

# 2. 个人开发分支且功能已完成
# MR已合并，分支使命完成，保持远程简洁

# 3. 临时同步操作
git merge main  # 仅为获取最新代码
# 不推送，避免不必要的远程提交
```

#### 7.3.3 推送前的检查清单

**推送前必做检查：**
```bash
# 1. 检查工作区状态
git status
# 确认没有不应该提交的文件

# 2. 检查提交历史
git log origin/分支名..HEAD --oneline
# 确认要推送的提交都是必要的

# 3. 检查配置文件
git diff HEAD~1 -- .env*
# 确认没有意外包含配置文件更改

# 4. 检查分支用途
# 个人分支 vs 团队分支 vs 功能分支
```

**安全推送流程：**
```bash
# 1. 保存本地配置（如果有）
git stash push -m "本地开发配置" -- .env* vite.config.ts

# 2. 推送代码
git push origin Dev-Wjj

# 3. 恢复本地配置
git stash pop

# 4. 验证推送结果
git log origin/Dev-Wjj --oneline -3
```

### 7.4 实际操作指南

#### 7.4.1 更新本地主分支
```bash
# 切换到main分支
git checkout main

# 拉取最新更改
git pull origin main
```

#### 7.4.2 更新开发分支
```bash
# 切换回开发分支
git checkout Dev-Wjj

# 合并最新main分支（仅本地）
git merge main

# 检查合并结果
git log --oneline -3
```

#### 7.4.3 推送决策实践
```bash
# 方法1: 分析提交内容
git log origin/Dev-Wjj..HEAD --oneline
git show --name-only HEAD  # 查看最新提交的文件

# 方法2: 检查文件差异
git diff origin/Dev-Wjj..HEAD --name-only

# 方法3: 模拟推送（不实际推送）
git push --dry-run origin Dev-Wjj

# 根据分析结果决定是否推送
if [[ "个人分支" && "有配置文件" ]]; then
    echo "不推送"
elif [[ "团队协作" || "需要CI/CD" ]]; then
    echo "推送"
    git push origin Dev-Wjj
else
    echo "根据具体情况决定"
fi
```

### 7.4 处理不需要的提交
```bash
# 撤销最后一个提交（保留文件更改）
git reset --soft HEAD~1

# 撤销最后一个提交（丢弃文件更改）
git reset --hard HEAD~1
```

---

## 8. 最佳实践

### 8.1 分支管理
- ✅ 功能分支基于最新main分支创建
- ✅ 分支命名清晰，体现功能或开发者
- ✅ 及时合并main分支，避免分歧过大
- ❌ 不要在main分支直接开发

### 8.2 提交管理
- ✅ 提交信息清晰，遵循规范
- ✅ 逻辑相关的更改放在同一提交
- ✅ 及时提交，避免大量更改堆积
- ❌ 不要提交临时文件、配置文件

### 8.3 配置文件处理
- ✅ 本地开发配置保持在工作区，不提交
- ✅ 通用配置更新可以提交
- ✅ 使用.gitignore忽略临时文件
- ❌ 不要提交个人开发环境配置

### 8.4 分支同步最佳实践

#### 8.4.1 个人开发分支策略
```bash
# 推荐做法：本地同步，不推送
git checkout Dev-Wjj
git merge main          # 获取最新代码
# 不执行 git push     # 保持远程分支简洁
```

**优势：**
- ✅ 本地开发环境始终最新
- ✅ 远程分支保持纯净的功能历史
- ✅ 避免配置文件冲突
- ✅ 减少不必要的远程提交

#### 8.4.2 团队协作分支策略
```bash
# 多人协作时：同步并推送
git checkout feature-branch
git merge main
git push origin feature-branch  # 让团队成员获得最新代码
```

#### 8.4.3 分支生命周期管理
```
创建分支 → 功能开发 → 本地同步main → MR合并 → 分支归档
    ↓         ↓          ↓           ↓         ↓
  推送分支   推送功能   不推送同步   删除远程   保留本地
```

### 8.5 MR流程
- ✅ MR描述详细，包含功能说明
- ✅ 合并前确保代码测试通过
- ✅ 及时响应代码审查意见
- ❌ 不要强制推送覆盖他人更改

### 8.6 远程分支清理
```bash
# MR合并后，清理远程分支
git push origin --delete Dev-Wjj

# 清理本地的远程跟踪分支
git remote prune origin

# 查看需要清理的分支
git branch -r --merged main
```

---

## 9. 常见问题和解决方案

### 9.1 合并冲突
**问题**：合并时出现冲突
```
Auto-merging src/config/index.ts
CONFLICT (content): Merge conflict in src/config/index.ts
```

**解决方案**：
1. 打开冲突文件
2. 查找冲突标记 `<<<<<<<`, `=======`, `>>>>>>>`
3. 手动选择或合并代码
4. 删除冲突标记
5. `git add` 标记解决
6. `git commit` 完成合并

### 9.2 推送被拒绝
**问题**：`git push` 被拒绝
```
! [rejected] Dev-Wjj -> Dev-Wjj (non-fast-forward)
```

**解决方案**：
```bash
# 先拉取远程更改
git pull origin Dev-Wjj

# 解决可能的冲突后再推送
git push origin Dev-Wjj
```

### 9.3 误提交配置文件
**问题**：不小心提交了本地配置文件

**解决方案**：
```bash
# 撤销最后一次提交，保留更改
git reset --soft HEAD~1

# 从暂存区移除配置文件
git restore --staged .env.development

# 重新提交其他文件
git commit -m "正确的提交信息"
```

### 9.4 分支同步提示
**问题**：IDE显示"同步更新N个提交"

**分析**：
- 如果是已合并到main的提交：不需要同步
- 如果是本地配置更改：不建议同步
- 如果是新功能提交：需要同步

**解决方案**：
- 分析提交内容：`git log origin/分支名..HEAD --oneline`
- 根据内容决定是否同步

### 9.5 Stash丢失问题
**问题**：应用stash后部分文件消失

**原因**：文件被.gitignore规则忽略

**解决方案**：
1. 临时注释.gitignore中的相关规则
2. 重新应用stash
3. 决定是否保留这些文件

### 9.6 分支同步相关问题

#### 9.6.1 本地合并后其他人看不到更新
**问题**：我执行了`git merge main`，但同事拉取我的分支时看不到最新代码

**原因分析**：
```bash
# 您的本地状态
git log --oneline -2
93981a4 (HEAD -> Dev-Wjj) 最新合并
3827c20 功能提交

# 远程分支状态
git log origin/Dev-Wjj --oneline -2
3827c20 (origin/Dev-Wjj) 功能提交

# 同事拉取到的
git pull origin Dev-Wjj  # 只能获得 3827c20
```

**解决方案**：
```bash
# 如果需要共享，推送合并结果
git push origin Dev-Wjj

# 如果不需要共享，告知同事直接拉取main分支
git checkout main
git pull origin main
```

#### 9.6.2 IDE显示"同步更新N个提交"
**问题**：合并main分支后，IDE提示需要同步多个提交

**分析方法**：
```bash
# 查看具体是哪些提交
git log origin/Dev-Wjj..HEAD --oneline

# 分析提交类型
# - 如果是main分支的合并提交：通常不需要推送
# - 如果是配置文件更改：不建议推送
# - 如果是新功能提交：考虑推送
```

**决策原则**：
- ✅ **个人开发分支**：忽略同步提示
- ✅ **功能已完成**：不需要推送
- 🤔 **团队协作**：根据需要决定

#### 9.6.3 本地合并后是否推送的困惑
**问题**：执行`git merge main`后，不确定是否应该推送到远程

**决策框架**：
```bash
# 第一步：检查当前状态
git status
git log origin/分支名..HEAD --oneline

# 第二步：应用决策矩阵
echo "检查以下条件："
echo "1. 是否有未提交的配置文件？"
echo "2. 是否为个人开发分支？"
echo "3. 功能是否已完成（MR已合并）？"
echo "4. 是否需要团队协作？"
```

**快速决策表**：
```
个人分支 + 配置文件 + 功能完成 = 不推送 ❌
团队分支 + 无配置文件 + 正在开发 = 推送 ✅
个人分支 + 无配置文件 + 临时同步 = 不推送 ❌
功能分支 + 需要CI/CD = 推送 ✅
```

**实际案例分析**：
```bash
# 案例1: 您的当前情况
git status  # 显示配置文件被修改
# 结论：不推送，保持本地开发环境

# 案例2: 团队功能开发
git status  # 工作区干净
git log --oneline -2  # 包含重要功能更新
# 结论：推送，让团队获得最新代码
```

#### 9.6.4 远程分支混乱
**问题**：远程分支包含了很多不相关的提交

**预防措施**：
```bash
# 创建功能分支时基于最新main
git checkout main
git pull origin main
git checkout -b new-feature

# 功能完成后及时删除远程分支
git push origin --delete old-feature
```

**清理方法**：
```bash
# 删除混乱的远程分支
git push origin --delete Dev-Wjj

# 重新创建干净的分支
git checkout -b Dev-Wjj-clean
git push -u origin Dev-Wjj-clean
```

---

## 总结

这个完整的Git协作流程涵盖了：
- ✅ 规范的分支管理
- ✅ 清晰的提交历史
- ✅ 有效的冲突解决
- ✅ 合理的配置文件处理
- ✅ 完整的MR流程

遵循这些实践可以确保团队协作的顺畅和代码质量的稳定。

---

## 附录

### A. 常用Git命令速查表

```bash
# 分支操作
git branch                    # 查看本地分支
git branch -a                 # 查看所有分支
git checkout -b 分支名        # 创建并切换分支
git checkout 分支名           # 切换分支
git branch -d 分支名          # 删除本地分支
git push origin --delete 分支名  # 删除远程分支

# 提交操作
git status                    # 查看状态
git add .                     # 添加所有更改
git add 文件名                # 添加特定文件
git commit -m "提交信息"      # 提交更改
git commit --amend            # 修改最后一次提交

# 同步操作
git fetch origin              # 获取远程更新
git pull origin 分支名        # 拉取并合并
git push origin 分支名        # 推送到远程
git merge 分支名              # 合并分支（仅本地）

# 分支状态检查
git log origin/分支名..HEAD --oneline  # 查看本地领先的提交
git log HEAD..origin/分支名 --oneline   # 查看远程领先的提交
git branch -vv                          # 查看分支跟踪关系

# 历史查看
git log --oneline             # 查看提交历史
git log --graph --oneline     # 图形化查看历史
git show 提交ID               # 查看特定提交详情

# Stash操作
git stash                     # 保存当前更改
git stash push -m "描述"      # 保存并添加描述
git stash list                # 查看stash列表
git stash pop                 # 应用并删除最新stash
git stash apply stash@{0}     # 应用特定stash
git stash drop stash@{0}      # 删除特定stash

# 撤销操作
git reset --soft HEAD~1       # 撤销提交，保留更改
git reset --hard HEAD~1       # 撤销提交，丢弃更改
git restore 文件名            # 撤销工作区更改
git restore --staged 文件名   # 撤销暂存区更改
```

### B. 项目结构说明

基于本次知识库功能开发，项目的典型结构：

```
src/
├── api/iot/                  # API接口层
│   └── knowledgeBase.ts      # 知识库相关API
├── views/ai/                 # AI功能页面
│   ├── kb/                   # 知识库模块
│   │   ├── fm/index.vue      # 文件管理
│   │   ├── kbm/index.vue     # 知识库管理
│   │   └── kbs/index.vue     # 知识库搜索
│   └── llm/                  # 大语言模型模块
│       └── chat/index.vue    # AI聊天
├── composables/              # 组合函数
│   └── useAuth.ts            # 认证相关
├── config/                   # 配置文件
│   └── knowledgeBase.ts      # 知识库配置
├── router/                   # 路由配置
├── stores/                   # 状态管理
└── utils/                    # 工具函数
```

### C. 团队协作规范

#### C.1 分支命名规范
- `main` - 主分支，生产环境代码
- `Dev-{姓名}` - 个人开发分支
- `feature/{功能名}` - 功能开发分支
- `hotfix/{问题描述}` - 紧急修复分支
- `release/{版本号}` - 发布分支

#### C.2 提交信息规范
```
<type>(<scope>): <subject>

<body>

<footer>
```

**Type类型：**
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### C.3 代码审查清单
- [ ] 代码符合项目规范
- [ ] 功能测试通过
- [ ] 没有明显的性能问题
- [ ] 没有安全隐患
- [ ] 文档更新完整
- [ ] 没有调试代码残留

### D. 故障排除指南

#### D.1 常见错误信息及解决方案

**错误1：`fatal: not a git repository`**
```bash
# 解决方案：初始化git仓库或进入正确目录
git init
# 或
cd 正确的项目目录
```

**错误2：`Your branch is behind 'origin/main' by N commits`**
```bash
# 解决方案：拉取最新更改
git pull origin main
```

**错误3：`Please commit your changes or stash them before you switch branches`**
```bash
# 解决方案：提交或保存更改
git stash
# 或
git add . && git commit -m "临时提交"
```

**错误4：`merge conflict in file.txt`**
```bash
# 解决方案：手动解决冲突
# 1. 编辑冲突文件
# 2. 删除冲突标记
# 3. git add file.txt
# 4. git commit
```

#### D.2 紧急情况处理

**情况1：误删重要文件**
```bash
# 如果还未提交
git restore 文件名

# 如果已提交但未推送
git reset --hard HEAD~1

# 如果已推送，从历史恢复
git checkout 提交ID -- 文件名
```

**情况2：需要回滚到之前版本**
```bash
# 查看历史
git log --oneline

# 回滚到特定提交（创建新提交）
git revert 提交ID

# 强制回滚（危险操作）
git reset --hard 提交ID
```

---

**文档版本**：v1.0
**创建时间**：2025-08-12
**最后更新**：2025-08-12
**适用场景**：Vue.js项目的Git协作开发
**维护者**：开发团队
