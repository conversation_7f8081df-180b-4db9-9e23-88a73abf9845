// src/api/gateway/driver.ts
import request from '@/utils/request'

// 获取插件信息列表
export function getPluginInfos(params?: { name?: string; type?: string }) {
  // 构建查询参数
  const queryParams = new URLSearchParams()
  queryParams.append('Current', '1')
  queryParams.append('Size', '1000')
  if (params?.name) queryParams.append('name', params.name)
  if (params?.type) queryParams.append('type', params.type)

  // 动态获取当前主机地址，确保同一网段下的其他电脑能访问
  const host = window.location.hostname
  const port = '5000' // 后端服务端口
  const url = `http://${host}:${port}/openApi/runtimeInfo/getPluginInfos?${queryParams.toString()}`

  return fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.json()
  }).then(data => {
    // ThingsGateway返回的是SqlSugarPagedList格式
    if (data && data.data && data.data.records && Array.isArray(data.data.records)) {
      const records = data.data.records.map((item: any) => ({
        fileName: item.fileName || item.name,
        name: item.name || item.fullName,
        version: item.version || '1.0.0',
        lastWriteTime: item.lastWriteTime || new Date().toISOString(),
        type: item.pluginType === 1 ? '采集驱动' : '业务驱动'
      }))
      return {
        data: records,
        code: 200,
        msg: 'success'
      }
    } else {
      return {
        data: [],
        code: 200,
        msg: 'success'
      }
    }
  })
}