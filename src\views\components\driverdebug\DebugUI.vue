<template>
    <div class="debug-ui">
      <div class="debug-ui-header">
        <h4>调试UI信息</h4>
      </div>
  
      <div class="debug-ui-content">
        <!-- 调试UI状态 -->
        <el-card class="ui-status-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>调试UI状态</span>
                             <el-tag 
                 :type="debugUIInfo && debugUIInfo.hasDebugUI ? 'success' : 'info'"
                 size="small"
               >
                 {{ debugUIInfo && debugUIInfo.hasDebugUI ? '可用' : '不可用' }}
               </el-tag>
            </div>
          </template>
  
          <div class="ui-info">
            <el-descriptions :column="1" border size="small">
                             <el-descriptions-item label="调试UI类型">
                 {{ debugUIInfo && debugUIInfo.debugUITypeName || '无' }}
               </el-descriptions-item>
               <el-descriptions-item label="地址UI类型">
                 {{ debugUIInfo && debugUIInfo.addressUITypeName || '无' }}
               </el-descriptions-item>
               <el-descriptions-item label="错误信息" v-if="debugUIInfo && debugUIInfo.errorMessage">
                 <el-tag type="danger" size="small">
                   {{ debugUIInfo.errorMessage }}
                 </el-tag>
               </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
  
        <!-- 调试功能 -->
        <div v-if="debugUIInfo && debugUIInfo.hasDebugUI" class="debug-functions">
          <el-card class="function-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>调试功能</span>
              </div>
            </template>
  
            <div class="function-buttons">
              <el-button 
                type="primary" 
                @click="openDebugWindow"
                :icon="Monitor"
              >
                打开调试窗口
              </el-button>
              
              <el-button 
                type="success" 
                @click="refreshDebugUI"
                :icon="Refresh"
              >
                刷新调试UI
              </el-button>
              
              <el-button 
                type="warning" 
                @click="exportDebugConfig"
                :icon="Download"
              >
                导出配置
              </el-button>
            </div>
  
            <!-- 调试窗口 -->
            <div v-if="showDebugWindow" class="debug-window">
              <div class="window-header">
                <span>调试窗口 - {{ pluginName }}</span>
                <el-button 
                  type="text" 
                  @click="closeDebugWindow"
                  :icon="Close"
                  size="small"
                />
              </div>
              <div class="window-content">
                <el-empty description="调试UI内容将在这里显示">
                  <el-icon size="40"><Monitor /></el-icon>
                </el-empty>
              </div>
            </div>
          </el-card>
        </div>
  
        <!-- 无调试UI时的提示 -->
        <div v-else class="no-debug-ui">
          <el-empty description="该插件不支持调试UI">
            <el-icon size="40"><Monitor /></el-icon>
          </el-empty>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Monitor, Refresh, Download, Close } from '@element-plus/icons-vue'
  import type { DebugUIInfo } from '@/api/gateway/driverdebug'
  
  // Props
  interface Props {
    pluginName: string
    debugUIInfo: DebugUIInfo
  }
  
  const props = defineProps<Props>()
  
  // 响应式数据
  const showDebugWindow = ref(false)
  
  // 打开调试窗口
  const openDebugWindow = () => {
    showDebugWindow.value = true
    ElMessage.success('调试窗口已打开')
  }
  
  // 关闭调试窗口
  const closeDebugWindow = () => {
    showDebugWindow.value = false
    ElMessage.info('调试窗口已关闭')
  }
  
  // 刷新调试UI
  const refreshDebugUI = () => {
    ElMessage.success('调试UI已刷新')
  }
  
  // 导出配置
  const exportDebugConfig = () => {
    const config = {
      pluginName: props.pluginName,
      debugUIInfo: props.debugUIInfo,
      timestamp: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(config, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `debug-config-${props.pluginName}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    ElMessage.success('配置已导出')
  }
  
  </script>

<script lang="ts">
export default {
  name: 'DebugUI'
}
</script>
  
  <style scoped>
  .debug-ui {
    padding: 16px;
  }
  
  .debug-ui-header {
    margin-bottom: 20px;
  }
  
  .debug-ui-header h4 {
    margin: 0;
    color: #303133;
  }
  
  .debug-ui-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .ui-status-card {
    border: 1px solid #e4e7ed;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .ui-info {
    padding: 8px 0;
  }
  
  .debug-functions {
    margin-top: 16px;
  }
  
  .function-card {
    border: 1px solid #e4e7ed;
  }
  
  .function-buttons {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
  }
  
  .debug-window {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .window-header {
    background-color: #f5f7fa;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e4e7ed;
    font-weight: 500;
    color: #303133;
  }
  
  .window-content {
    padding: 40px;
    background-color: white;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .no-debug-ui {
    text-align: center;
    padding: 40px 0;
  }
  
  :deep(.el-card__header) {
    padding: 12px 16px;
    background-color: #fafafa;
  }
  
  :deep(.el-descriptions__label) {
    font-weight: 500;
  }
  </style> 