<template>
  <div class="driver-page">
    <div class="driver-form-container">
      <el-form :model="form" label-width="80px" class="driver-form">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="插件名称">
              <el-input v-model="form.name" placeholder="请输入插件名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="插件类型">
              <el-select v-model="form.type" placeholder="请选择插件类型" clearable>
                <el-option
                  v-for="item in typeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="form-actions">
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="onReset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <div class="table-section">
      <div class="table-header">
        <h2 class="table-title">驱动列表</h2>
        <div class="table-controls">
          <el-button 
            type="primary" 
            @click="handleUpload"
            class="upload-button"
          >
            <el-icon><Upload /></el-icon>
            上传文件
          </el-button>
          <el-button 
            type="text" 
            @click="toggleColumnSettings"
            class="settings-button"
          >
            <el-icon><Setting /></el-icon>
            列设置
          </el-button>
        </div>
      </div>
      
      <div class="table-container">
        <div class="column-settings-wrapper">
          <el-collapse-transition>
            <div v-show="showColumnSettings" class="column-settings-panel">
              <el-checkbox-group v-model="visibleColumns" @change="handleColumnChange">
                <el-checkbox 
                  v-for="column in allColumns" 
                  :key="column.prop" 
                  :label="column.prop"
                  :disabled="visibleColumns.length === 1 && visibleColumns.includes(column.prop)"
                >
                  {{ column.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </el-collapse-transition>
        </div>
        
        <el-table 
          :data="tableData" 
          style="width: 100%" 
          border
          v-loading="loading"
          element-loading-text="加载中..."
          class="driver-table"
        >
          <el-table-column 
            v-if="visibleColumns.includes('index')" 
            type="index" 
            label="行号" 
            width="80" 
          />
          <el-table-column 
            v-if="visibleColumns.includes('fileName')" 
            prop="fileName" 
            label="插件文件" 
          />
          <el-table-column 
            v-if="visibleColumns.includes('type')" 
            prop="type" 
            label="插件类型" 
          />
          <el-table-column 
            v-if="visibleColumns.includes('name')" 
            prop="name" 
            label="插件名称" 
          />
          <el-table-column 
            v-if="visibleColumns.includes('version')" 
            prop="version" 
            label="插件版本" 
          />
          <el-table-column 
            v-if="visibleColumns.includes('lastWriteTime')" 
            prop="lastWriteTime" 
            label="插件编译时间" 
          />
        </el-table>
      </div>
    </div>
  </div>
</template>
  
  <script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { Setting, Upload } from '@element-plus/icons-vue'
  import { getPluginInfos } from '@/api/gateway/driver'
  
  const form = ref({
    name: '',
    type: ''
  })
  
  // 插件类型选项
  const typeOptions = [
    { label: '采集驱动', value: '1' },
    { label: '业务驱动', value: '0' }
  ]
  
  // 表格数据
  const tableData = ref<any[]>([])
  const loading = ref(false)

  // 列配置
  const allColumns = [
    { prop: 'index', label: '行号' },
    { prop: 'fileName', label: '插件文件' },
    { prop: 'type', label: '插件类型' },
    { prop: 'name', label: '插件名称' },
    { prop: 'version', label: '插件版本' },
    { prop: 'lastWriteTime', label: '插件编译时间' }
  ]
  
  // 可见列
  const visibleColumns = ref(['index', 'fileName', 'type', 'name', 'version', 'lastWriteTime'])
  
  // 显示列设置面板
  const showColumnSettings = ref(false)
  
  // 列变化处理
  function handleColumnChange(value: string[]) {
    console.log('列显示状态变化:', value)
  }
  
  // 切换列设置面板显示
  function toggleColumnSettings() {
    showColumnSettings.value = !showColumnSettings.value
  }

  // 上传文件处理
  function handleUpload() {
    console.log('上传文件')
    // 实现上传文件的逻辑
  }
  
  // 获取插件信息列表
  async function fetchPluginInfos() {
    try {
      loading.value = true
      console.log('开始请求插件信息，表单数据:', form.value)
      const response = await getPluginInfos({
        name: form.value.name || undefined
      })
      console.log('API响应:', response)
      
      // 确保数据是数组格式
      if (response && response.data) {
        if (Array.isArray(response.data)) {
          // 前端过滤数据
          let filteredData = response.data
          
          // 按插件类型过滤
          if (form.value.type) {
            const targetType = form.value.type === '1' ? '采集驱动' : '业务驱动'
            filteredData = filteredData.filter((item: any) => item.type === targetType)
          }
          
          // 按插件名称过滤（如果后端不支持name参数，也可以在前端过滤）
          if (form.value.name) {
            filteredData = filteredData.filter((item: any) => 
              item.name.toLowerCase().includes(form.value.name.toLowerCase()) ||
              item.fileName.toLowerCase().includes(form.value.name.toLowerCase())
            )
          }
          
          tableData.value = filteredData
          console.log('过滤后的数据:', tableData.value)
        } else {
          console.error('API返回的数据不是数组格式:', response.data)
          tableData.value = []
        }
      } else {
        console.error('API响应格式错误:', response)
        tableData.value = []
      }
    } catch (error) {
      console.error('请求插件信息出错，详细错误:', error)
      if (error instanceof Error) {
        console.error('错误堆栈:', error.stack)
      }
      tableData.value = []
    } finally {
      loading.value = false
    }
  }
  
  function onSearch() {
    // 查询逻辑
    console.log('查询', form.value)
    fetchPluginInfos()
  }
  
  function onReset() {
    form.value.name = ''
    form.value.type = ''
    fetchPluginInfos()
  }
  
  // 组件挂载时获取数据
  onMounted(() => {
    fetchPluginInfos()
  })
  </script>
  
  <style scoped>
  .driver-page {
    padding: 24px;
    background: #f5f7fa;
    min-height: 100vh;
  }
  .driver-form-container {
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px #f0f1f2;
    margin-bottom: 24px;
  }
  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e4e7ed;
  }
  .form-title {
    font-size: 20px;
    font-weight: bold;
    color: #000;
    margin: 0;
  }
  .driver-form {
    max-width: 900px;
    margin: 0;
  }
  .form-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    height: 100%;
  }
   
  .table-section {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px #f0f1f2;
    padding: 24px;
  }
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  .table-title {
    font-size: 20px;
    font-weight: bold;
    color: #000;
    margin: 0;
  }
  .table-controls {
    display: flex;
    align-items: center;
    gap: 12px;
  }
   
  .table-container {
    position: relative;
  }
   
  .column-settings-wrapper {
    position: relative;
    margin-bottom: 16px;
  }
   
  .column-settings-panel {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 10;
    background: #fff;
    padding: 12px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border: 1px solid #e4e7ed;
    min-width: 200px;
    animation: slideDown 0.3s ease-out;
  }
   
  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .column-settings-panel .el-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .column-settings-panel .el-checkbox {
    margin: 0;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    background: #f8f9fa;
    border: 1px solid #e4e7ed;
  }
  
  .column-settings-panel .el-checkbox:hover {
    background: #e9ecef;
    transform: translateX(4px);
  }
  
  .column-settings-panel .el-checkbox.is-checked {
    background: #e3f2fd;
    border-color: #667eea;
  }
  
  .column-settings-panel .el-checkbox__label {
    color: #000;
    font-weight: 500;
    font-size: 14px;
  }
  
  .column-settings-panel .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #667eea;
    border-color: #667eea;
  }
  
  .column-settings-panel .el-checkbox__input.is-checked .el-checkbox__inner::after {
    border-color: #1e3a8a;
  }
 
  .driver-table {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .driver-table th {
    font-weight: bold !important;
    color: #000 !important;
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif !important;
    font-size: 14px !important;
    background-color: #f5f7fa !important;
    border-bottom: 2px solid #e4e7ed !important;
    padding: 12px 0 !important;
  }
 
 /* 全局表头样式 */
 :deep(.el-table th) {
   font-weight: bold !important;
   color: #000 !important;
   font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif !important;
   font-size: 14px !important;
   background-color: #f5f7fa !important;
   border-bottom: 2px solid #e4e7ed !important;
   padding: 12px 0 !important;
 }
 
 /* 表格行样式 */
 .driver-table .el-table__row {
   transition: background-color 0.3s ease;
 }
 
 .driver-table .el-table__row:hover {
   background-color: #f0f9ff !important;
 }
 
 /* 按钮样式优化 */
 .settings-button {
   display: flex;
   align-items: center;
   gap: 4px;
   font-size: 14px;
   color: #409eff;
   transition: color 0.3s ease;
 }
 
 .settings-button:hover {
   color: #66b1ff;
 }

 .upload-button {
   background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
   border: none;
   border-radius: 8px;
   padding: 10px 16px;
   color: #fff;
   font-weight: 500;
   box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
   transition: all 0.3s ease;
 }

 .upload-button:hover {
   transform: translateY(-2px);
   box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
   background: linear-gradient(135deg, #66b1ff 0%, #409eff 100%);
 }

 .upload-button:active {
   transform: translateY(0);
   box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
 }
 
 .upload-button .el-icon {
   font-size: 16px;
   color: #fff;
 }
 
 /* 表单样式优化 */
 .driver-form .el-form-item {
   margin-bottom: 18px;
 }
 
 .driver-form .el-input,
 .driver-form .el-select {
   width: 100%;
 }
 
 .driver-form .el-form-item__label {
    color: #000 !important;
    font-weight: bold !important;
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif !important;
    font-size: 14px !important;
  }
 
 /* 使用更强的选择器确保表单标签样式生效 */
 :deep(.driver-form .el-form-item__label) {
   color: #000 !important;
   font-weight: bold !important;
   font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif !important;
   font-size: 14px !important;
 }
 
 /* 全局表单标签样式 */
 :deep(.el-form-item__label) {
   color: #000 !important;
   font-weight: bold !important;
   font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif !important;
   font-size: 14px !important;
 }
 
 /* 响应式布局 */
 @media (max-width: 768px) {
   .driver-page {
     padding: 16px;
   }
   
   .driver-form-container,
   .table-section {
     padding: 16px;
   }
   
   .form-title,
   .table-title {
     font-size: 18px;
   }
 }
 
 .column-settings-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #fff;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }
   
  .column-settings-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }
   
  .column-settings-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  }
   
  .column-settings-btn .el-icon {
    font-size: 18px;
    color: #fff;
  }
  </style>