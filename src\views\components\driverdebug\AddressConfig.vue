<template>
    <div class="address-config">
      <div class="address-config-header">
        <h4>地址配置</h4>
      </div>
  
      <div class="address-config-content">
        <!-- 地址UI状态 -->
        <el-card class="address-status-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>地址UI状态</span>
                             <el-tag 
                 :type="addressUIInfo && addressUIInfo.hasAddressUI ? 'success' : 'info'"
                 size="small"
               >
                 {{ addressUIInfo && addressUIInfo.hasAddressUI ? '可用' : '不可用' }}
               </el-tag>
            </div>
          </template>
  
          <div class="address-info">
            <el-descriptions :column="1" border size="small">
                             <el-descriptions-item label="地址UI类型">
                 {{ addressUIInfo && addressUIInfo.addressUITypeName || '无' }}
               </el-descriptions-item>
               <el-descriptions-item label="错误信息" v-if="addressUIInfo && addressUIInfo.errorMessage">
                 <el-tag type="danger" size="small">
                   {{ addressUIInfo.errorMessage }}
                 </el-tag>
               </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
  
        <!-- 地址配置功能 -->
        <div v-if="addressUIInfo && addressUIInfo.hasAddressUI" class="address-functions">
          <el-card class="function-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>地址配置功能</span>
              </div>
            </template>
  
            <div class="function-buttons">
              <el-button 
                type="primary" 
                @click="openAddressConfig"
                :icon="Edit"
              >
                打开地址配置
              </el-button>
              
              <el-button 
                type="success" 
                @click="validateAddress"
                :icon="Check"
              >
                验证地址
              </el-button>
              
              <el-button 
                type="warning" 
                @click="exportAddressConfig"
                :icon="Download"
              >
                导出地址配置
              </el-button>
            </div>
  
            <!-- 地址配置窗口 -->
            <div v-if="showAddressConfig" class="address-config-window">
              <div class="window-header">
                <span>地址配置 - {{ pluginName }}</span>
                <el-button 
                  type="text" 
                  @click="closeAddressConfig"
                  :icon="Close"
                  size="small"
                />
              </div>
              <div class="window-content">
                <el-empty description="地址配置UI将在这里显示">
                  <el-icon size="40"><Edit /></el-icon>
                </el-empty>
              </div>
            </div>
          </el-card>
        </div>
  
        <!-- 无地址UI时的提示 -->
        <div v-else class="no-address-ui">
          <el-empty description="该插件不支持地址配置UI">
            <el-icon size="40"><Edit /></el-icon>
          </el-empty>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Edit, Check, Download, Close } from '@element-plus/icons-vue'
  import type { DebugUIInfo } from '@/api/gateway/driverdebug'
  
  // Props
  interface Props {
    pluginName: string
    addressUIInfo: DebugUIInfo
  }
  
  const props = defineProps<Props>()
  
  // 响应式数据
  const showAddressConfig = ref(false)
  
  // 打开地址配置
  const openAddressConfig = () => {
    showAddressConfig.value = true
    ElMessage.success('地址配置窗口已打开')
  }
  
  // 关闭地址配置
  const closeAddressConfig = () => {
    showAddressConfig.value = false
    ElMessage.info('地址配置窗口已关闭')
  }
  
  // 验证地址
  const validateAddress = () => {
    ElMessage.success('地址验证完成')
  }
  
  // 导出地址配置
  const exportAddressConfig = () => {
    const config = {
      pluginName: props.pluginName,
      addressUIInfo: props.addressUIInfo,
      timestamp: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(config, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `address-config-${props.pluginName}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    ElMessage.success('地址配置已导出')
  }
  
  </script>

<script lang="ts">
export default {
  name: 'AddressConfig'
}
</script>
  
  <style scoped>
  .address-config {
    padding: 16px;
  }
  
  .address-config-header {
    margin-bottom: 20px;
  }
  
  .address-config-header h4 {
    margin: 0;
    color: #303133;
  }
  
  .address-config-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .address-status-card {
    border: 1px solid #e4e7ed;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .address-info {
    padding: 8px 0;
  }
  
  .address-functions {
    margin-top: 16px;
  }
  
  .function-card {
    border: 1px solid #e4e7ed;
  }
  
  .function-buttons {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
  }
  
  .address-config-window {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .window-header {
    background-color: #f5f7fa;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e4e7ed;
    font-weight: 500;
    color: #303133;
  }
  
  .window-content {
    padding: 40px;
    background-color: white;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .no-address-ui {
    text-align: center;
    padding: 40px 0;
  }
  
  :deep(.el-card__header) {
    padding: 12px 16px;
    background-color: #fafafa;
  }
  
  :deep(.el-descriptions__label) {
    font-weight: 500;
  }
  </style> 