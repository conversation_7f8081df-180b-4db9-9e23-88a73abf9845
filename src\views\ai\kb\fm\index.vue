1<template>
  <div class="file-management-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">文件管理</h2>
        <div class="breadcrumb">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>知识库管理</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentKnowledgeBase?.name || '文件管理' }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>

      <div class="header-actions">
        <el-select
          v-model="selectedKbId"
          placeholder="选择知识库"
          @change="handleKbChange"
          style="width: 200px"
        >
          <el-option
            v-for="kb in knowledgeBases"
            :key="kb.id"
            :label="kb.name"
            :value="kb.id"
          />
        </el-select>

        <!-- 文档统计信息 -->
        <div class="document-stats-inline" v-if="selectedKbId">
          <div class="stat-item-inline">
            <span class="stat-label">总计:</span>
            <span class="stat-number">{{ documentStats.total }}</span>
          </div>
          <div class="stat-item-inline">
            <span class="stat-label">已解析:</span>
            <span class="stat-number success">{{ documentStats.parsed }}</span>
          </div>
          <div class="stat-item-inline">
            <span class="stat-label">解析中:</span>
            <span class="stat-number warning">{{ documentStats.parsing }}</span>
          </div>
          <div class="stat-item-inline">
            <span class="stat-label">失败:</span>
            <span class="stat-number danger">{{ documentStats.failed }}</span>
          </div>
        </div>

        <!-- 快速操作按钮组 -->
        <div class="quick-actions" v-if="selectedKbId">
          <el-button
            type="success"
            :icon="VideoPlay"
            @click="startBatchParsing"
            :disabled="!canBatchParse"
            :loading="batchParsing"
            size="small"
          >
            批量解析
          </el-button>
          <el-button
            type="warning"
            :icon="VideoPause"
            @click="stopBatchParsing"
            :disabled="!canBatchStop"
            :loading="batchStopping"
            size="small"
          >
            停止解析
          </el-button>
          <el-button
            type="info"
            :icon="Refresh"
            @click="refreshDocuments"
            :loading="refreshing"
            size="small"
          >
            刷新
          </el-button>
        </div>

        <el-button
          type="primary"
          :icon="Upload"
          @click="showUploadDialog = true"
          :disabled="!selectedKbId"
        >
          上传文档
        </el-button>

        <el-button
          :icon="Setting"
          @click="showSettings = !showSettings"
        >
          设置
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 主内容区域 -->
      <div class="content-area">
        <!-- 文档列表 -->
        <DocumentList
          ref="documentListRef"
          :knowledge-base-id="selectedKbId"
          @document-select="handleDocumentSelect"
          @document-action="handleDocumentAction"
        />
      </div>
    </div>

    <!-- 上传对话框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="上传文档"
      width="800px"
      :close-on-click-modal="false"
    >
      <DocumentUpload
        :knowledge-base-id="selectedKbId"
        :show-config="true"
        @upload-success="handleUploadSuccess"
        @upload-error="handleUploadError"
        @upload-progress="handleUploadProgress"
      />
    </el-dialog>

    <!-- 文档预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      :title="previewDocument?.name || '文档预览'"
      width="90%"
      :close-on-click-modal="false"
      fullscreen
    >
      <DocumentPreview
        v-if="previewDocument"
        :document="previewDocument"
        :visible="showPreviewDialog"
        :show-settings="true"
        @download="handleDocumentDownload"
        @document-type-detected="handleDocumentTypeDetected"
      />
    </el-dialog>

    <!-- 设置面板 -->
    <el-drawer
      v-model="showSettings"
      title="文件管理设置"
      direction="rtl"
      size="400px"
    >
      <div class="settings-content">
        <el-form label-width="120px">


          <el-form-item label="显示缩略图">
            <el-switch v-model="showThumbnails" />
          </el-form-item>

          <el-form-item label="每页显示">
            <el-select v-model="pageSize">
              <el-option label="10条" :value="10" />
              <el-option label="20条" :value="20" />
              <el-option label="50条" :value="50" />
              <el-option label="100条" :value="100" />
            </el-select>
          </el-form-item>

          <el-form-item label="默认解析器">
            <el-select v-model="defaultParser">
              <el-option
                v-for="parser in parserOptions"
                :key="parser.value"
                :label="parser.label"
                :value="parser.value"
              />
            </el-select>
          </el-form-item>
        </el-form>

        <el-divider />

        <div class="settings-actions">
          <el-button @click="resetSettings">重置设置</el-button>
          <el-button type="primary" @click="saveSettings">保存设置</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElNotification } from 'element-plus';
import {
  Upload,
  Setting,
  VideoPlay,
  VideoPause,
  Refresh
} from '@element-plus/icons-vue';

// 导入组件
import DocumentUpload from '/@/components/FileUpload/DocumentUpload.vue';
import DocumentList from '/@/components/FileManagement/DocumentList.vue';
import DocumentPreview from '/@/components/FileManagement/DocumentPreview.vue';
import DocumentParseStatus from '/@/components/FileManagement/DocumentParseStatus.vue';

// 导入API
import {
  getKnowledgeBaseList,
  type KnowledgeBaseInfo
} from '/@/api/iot/knowledgeBase';
import {
  getParserOptions,
  type DocumentInfo
} from '/@/api/iot/document';

// 响应式数据
const selectedKbId = ref('');
const knowledgeBases = ref<KnowledgeBaseInfo[]>([]);
const currentKnowledgeBase = ref<KnowledgeBaseInfo | null>(null);

// UI状态
const activeTab = ref('list');
const showUploadDialog = ref(false);
const showPreviewDialog = ref(false);
const showSettings = ref(false);

// 文档相关
const selectedDocuments = ref<DocumentInfo[]>([]);
const previewDocument = ref<DocumentInfo | null>(null);


// 操作状态
const refreshing = ref(false);
const batchParsing = ref(false);
const batchStopping = ref(false);

// 设置
const showThumbnails = ref(true);
const pageSize = ref(20);
const defaultParser = ref('naive');

// 组件引用
const documentListRef = ref();
const parseStatusRef = ref();

// 解析器选项
const parserOptions = getParserOptions();



// 计算属性
const documentStats = computed(() => {
  const stats = {
    total: 0,
    parsed: 0,
    parsing: 0,
    failed: 0
  };

  selectedDocuments.value.forEach(doc => {
    stats.total++;
    if (doc.status === 'parsed') stats.parsed++;
    else if (doc.status === 'parsing') stats.parsing++;
    else if (doc.status === 'failed') stats.failed++;
  });

  return stats;
});

const canBatchParse = computed(() => {
  return selectedDocuments.value.some(doc =>
    doc.status === 'uploaded' || doc.status === 'failed'
  );
});

const canBatchStop = computed(() => {
  return selectedDocuments.value.some(doc => doc.status === 'parsing');
});

// 方法
const loadKnowledgeBases = async () => {
  try {
    const response = await getKnowledgeBaseList({
      page: 1,
      page_size: 100
    });

    const businessData = response.data;

    if (businessData.code === 200 && businessData.data) {
      knowledgeBases.value = businessData.data;

      // 如果有知识库且没有选中的，默认选择第一个
      if (knowledgeBases.value.length > 0 && !selectedKbId.value) {
        const firstKb = knowledgeBases.value[0];
        if (firstKb.id) {
          selectedKbId.value = firstKb.id;
          // 设置当前知识库信息
          currentKnowledgeBase.value = firstKb;
        }
      }
    } else {
      ElMessage.error(businessData.msg || businessData.message || '获取知识库列表失败');
    }
  } catch (error) {
    console.error('获取知识库列表失败:', error);
    ElMessage.error('获取知识库列表失败');
  }
};

const handleKbChange = (kbId: string) => {
  const kb = knowledgeBases.value.find(k => k.id === kbId);
  currentKnowledgeBase.value = kb || null;

  // 清空选中的文档
  selectedDocuments.value = [];

  // 文档列表会通过watch监听knowledgeBaseId变化自动刷新，无需手动调用
};

const handleTabChange = (tabName: string) => {
  activeTab.value = tabName;

  // 当切换到解析状态标签页时，自动刷新数据
  if (tabName === 'status' && parseStatusRef.value) {
    parseStatusRef.value.refreshStatus();
  }

  // 当切换到文档列表标签页时，也刷新数据
  if (tabName === 'list' && documentListRef.value) {
    documentListRef.value.refreshList();
  }
};

const handleDocumentSelect = (documents: DocumentInfo[]) => {
  selectedDocuments.value = documents;
};

const handleDocumentAction = (action: string, document: DocumentInfo) => {
  switch (action) {
    case 'preview':
      previewDocument.value = document;
      showPreviewDialog.value = true;
      break;
    case 'download':
      handleDocumentDownload(document);
      break;
    // 其他操作...
  }
};

const handleStatusChange = (_documents: DocumentInfo[]) => {
  // 状态变化处理
};

const handleParseComplete = (document: DocumentInfo) => {
  ElNotification({
    title: '解析完成',
    message: `文档 "${document.name}" 解析完成`,
    type: 'success'
  });


};

const handleParseError = (document: DocumentInfo, error: string) => {
  ElNotification({
    title: '解析失败',
    message: `文档 "${document.name}" 解析失败: ${error}`,
    type: 'error'
  });


};

// 防重复处理的标记
let uploadSuccessProcessing = false;

const handleUploadSuccess = (files: any[]) => {
  console.log('handleUploadSuccess called with files:', files);

  // 防重复处理
  if (uploadSuccessProcessing) {
    console.log('Upload success already processing, skipping...');
    return;
  }

  uploadSuccessProcessing = true;

  try {
    ElMessage.success(`成功上传 ${files.length} 个文件`);
    showUploadDialog.value = false;





    // 刷新文档列表
    refreshDocuments();
  } finally {
    // 重置防重复标记，延迟一段时间以防止快速重复调用
    setTimeout(() => {
      uploadSuccessProcessing = false;
    }, 1000);
  }
};

const handleUploadError = (error: string) => {
  ElMessage.error(`上传失败: ${error}`);
};

const handleUploadProgress = (_progress: number) => {
  // 上传进度处理
};

const handleDocumentDownload = (document: DocumentInfo) => {
  ElMessage.success(`开始下载: ${document.name}`);
};

const handleDocumentTypeDetected = (type: 'portrait' | 'landscape') => {
  console.log('全屏预览检测到文档方向:', type);
  // 在全屏模式下，我们可以记录文档类型，但不需要调整布局
};

const refreshDocuments = () => {
  refreshing.value = true;

  if (documentListRef.value) {
    documentListRef.value.refreshList();
  }

  if (parseStatusRef.value) {
    parseStatusRef.value.refreshStatus();
  }

  setTimeout(() => {
    refreshing.value = false;
  }, 1000);
};

const startBatchParsing = async () => {
  try {
    // 如果当前在文档列表页面，使用DocumentList的批量解析
    if (activeTab.value === 'list' && documentListRef.value) {
      await documentListRef.value.batchStartParsing();

      // 批量解析成功后，自动切换到解析状态页面
      activeTab.value = 'status';

      // 延迟一下再刷新解析状态页面，确保标签页切换完成
      setTimeout(() => {
        if (parseStatusRef.value) {
          parseStatusRef.value.refreshStatus();
        }
      }, 100);
    }
    // 如果当前在解析状态页面，使用DocumentParseStatus的批量解析
    else if (activeTab.value === 'status' && parseStatusRef.value) {
      await parseStatusRef.value.startBatchParsing();
    }
  } catch (error) {
    console.error('Batch parsing error:', error);
  }
};

const stopBatchParsing = () => {
  if (parseStatusRef.value) {
    parseStatusRef.value.stopBatchParsing();
  }
};



const resetSettings = () => {
  showThumbnails.value = true;
  pageSize.value = 20;
  defaultParser.value = 'naive';

  ElMessage.success('设置已重置');
};

const saveSettings = () => {
  // 保存设置到本地存储
  const settings = {
    showThumbnails: showThumbnails.value,
    pageSize: pageSize.value,
    defaultParser: defaultParser.value
  };

  localStorage.setItem('fileManagementSettings', JSON.stringify(settings));
  ElMessage.success('设置已保存');
  showSettings.value = false;
};

const loadSettings = () => {
  const saved = localStorage.getItem('fileManagementSettings');
  if (saved) {
    try {
      const settings = JSON.parse(saved);
      showThumbnails.value = settings.showThumbnails ?? true;
      pageSize.value = settings.pageSize ?? 20;
      defaultParser.value = settings.defaultParser ?? 'naive';
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  }
};

// 生命周期
onMounted(() => {
  loadSettings();
  loadKnowledgeBases();
});


</script>

<style scoped>
.file-management-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: white;
  margin: 20px 20px 0 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.breadcrumb {
  font-size: 12px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

/* 文档统计信息样式 */
.document-stats-inline {
  display: flex;
  gap: 16px;
  align-items: center;
  padding: 8px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.stat-item-inline {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-item-inline .stat-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.stat-item-inline .stat-number {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.stat-item-inline .stat-number.success { color: #28a745; }
.stat-item-inline .stat-number.warning { color: #ffc107; }
.stat-item-inline .stat-number.danger { color: #dc3545; }

/* 快速操作按钮组样式 */
.quick-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  padding: 0 20px 20px 20px;
}

/* 主内容区域样式 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}







.settings-content {
  padding: 20px;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.settings-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .document-stats-inline {
    flex-wrap: wrap;
    gap: 12px;
  }

  .quick-actions {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .document-stats-inline {
    order: 1;
    width: 100%;
    justify-content: space-around;
  }

  .quick-actions {
    order: 2;
    width: 100%;
    justify-content: center;
  }

  .main-content {
    padding: 0 12px 12px 12px;
  }
}

/* 移除深色主题支持，保持与知识库管理页面一致的浅色主题 */

/* 动画效果 */
.stat-number {
  transition: color 0.3s ease;
}



/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-state .el-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
  text-align: center;
  line-height: 1.5;
}
</style>